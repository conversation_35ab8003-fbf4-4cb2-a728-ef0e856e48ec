<QuestDialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="https://vezel.dev/novadrop/dc/QuestDialog QuestDialog.xsd" id="1" huntingZoneId="45" voiceTypeId="0" xmlns="https://vezel.dev/novadrop/dc/QuestDialog">
    <Text id="1" huntingZoneId="0" prevId="0" villagerId="0" />
    <Text id="100" huntingZoneId="0" prevId="0" villagerId="0">
        <Page social="0">You helped restore {@Rgn:47001$$Tirkai Forest} and advanced Yasring's and the Mysterium's argon research in {@Rgn:45001$$Thrallhold}. <PERSON><PERSON><PERSON>, curious about the life energy coming from some argon structures tried to purify argonified animals, but failed.&lt;BR&gt;&lt;BR&gt;You witnessed the transformation of small argons into other forms, including inanimate buildings. The Mysterium asked you to find out more about this. You went to meet <PERSON><PERSON><PERSON>, but couldn't find her.</Page>
    </Text>
    <Text id="2" huntingZoneId="0" prevId="0" villagerId="0">
        <Page social="0">[(It's a message from Yasring.)&lt;BR&gt;&lt;BR&gt;I'm studying the argons in {@Rgn:45001$$Thrallhold}. I need your help.</Page>
    </Text>
    <Text id="3" huntingZoneId="245" prevId="2" villagerId="1022">
        <Page social="4">Yasring? She came here to pick up a few reagents and left again to take a look at some argon construction.</Page>
    </Text>
    <Text id="4" huntingZoneId="245" prevId="3" villagerId="2001">
        <Page social="4">&lt;PCNAME&gt;! I hope I didn't interrupt anything important, but I just had to have your help with this.&lt;NEXTPAGEBUTTON&gt;"No, I'm glad you wrote me."&lt;/NEXTPAGEBUTTON&gt;</Page>
        <Page social="4">I've been discovering things about these argon structures—things that would make Elinu wake up and weep.&lt;BR&gt;&lt;BR&gt;I wrote up some notes for Mitiel. He's been doing lots of research into what the argons left behind. Could you take it to him?</Page>
    </Text>
    <Text id="5" huntingZoneId="245" prevId="4" villagerId="2002">
        <Page social="4">Yes? Is there something you needed?&lt;NEXTPAGEBUTTON&gt;"This is from Yasring."&lt;/NEXTPAGEBUTTON&gt;</Page>
        <Page social="10">I'll look this over. Ronalt wants to meet you. You should find him.</Page>
    </Text>
    <Text id="6" huntingZoneId="245" prevId="5" villagerId="2003">
        <Page social="4">We're all working on different aspects of what the argons left behind—plants, animals, even buildings that have been horribly transformed.&lt;BR&gt;&lt;BR&gt;Kupan here found a source of bits of argon armor. Unfortunately, he ran into a problem and could use your help.</Page>
    </Text>
    <Text id="7" huntingZoneId="245" prevId="6" villagerId="2004">
        <Page social="4">That's right. I can source anything from soup to nuts. Mmm...I'm hungry.&lt;BR&gt;&lt;BR&gt;Some armor fragments were on their way, but are just a bit overdue.&lt;BR&gt;&lt;BR&gt;I hope they weren't attacked. Will you look for the shipment at the rendezvous site? Churimon should be with it.&lt;BR&gt;&lt;BR&gt;I'll just find some lunch.</Page>
    </Text>
    <Text id="8" huntingZoneId="245" prevId="7" villagerId="2005">
        <Page social="9">Kupan sent you? We're saved!&lt;BR&gt;&lt;BR&gt;Our back wheel got stuck, and when I saw monsters, I ran.&lt;BR&gt;&lt;BR&gt;Could you take a look at the wagon?</Page>
    </Text>
    <Text id="9" huntingZoneId="245" prevId="8" villagerId="2012">
        <Page social="0">(Churimon was right. The back wheel is stuck.)</Page>
    </Text>
    <Text id="10" huntingZoneId="245" prevId="9" villagerId="2005">
        <Page social="8">I saw it run off. Got to love enchanted wagons, eh?&lt;BR&gt;&lt;BR&gt;At least we didn't lose Kupan's shipment. Tell him we'll be there soon.&lt;BR&gt;&lt;BR&gt;Say, why don't you come see me at {@Rgn:45009$$Camp Insanity} some time? All the best argon research happens there.</Page>
    </Text>
    <Text id="11" huntingZoneId="245" prevId="10" villagerId="2009">
        <Page social="4">Your assistance with the wagon is much appreciated. Churimon's work might well benefit us all.&lt;BR&gt;&lt;BR&gt;As it happens, Churimon would like to speak with you again.</Page>
    </Text>
    <Text id="12" huntingZoneId="245" prevId="11" villagerId="2011">
        <Page social="9">&lt;PCNAME&gt;, I need {@Creature:245#2009$$Ronalt} to take a look at a new research site I stumbled on. I don't want him to get killed in the process, so please accompany him to my new &lt;font color='#0080FF'&gt;&lt;A href="markmap:loc=7023#-6907, -47629, -3938&amp;type=1&amp;name=Research Site"&gt;research site&lt;/A&gt;&lt;/font&gt;. Time is of the essence!</Page>
    </Text>
    <Text id="13" huntingZoneId="245" prevId="12" villagerId="2009">
        <Page social="4">What's all this about? I've never seen {@Creature:245#2011$$Churimon} so excited.&lt;BR&gt;&lt;BR&gt;A new research site, eh? Sounds intriguing. Go on ahead—I'll meet you at the &lt;font color='#0080FF'&gt;&lt;A href="markmap:loc=7023#-6907, -47629, -3938&amp;type=1&amp;name=Research Site"&gt;research site&lt;/A&gt;&lt;/font&gt;.</Page>
    </Text>
    <Text id="14" huntingZoneId="245" prevId="13" villagerId="2010">
        <Page social="10">This is terrible! If my readings here are accurate, anything can become argonified. Anything at all.&lt;BR&gt;&lt;BR&gt;Get back to camp, but get me some &lt;font color='#0080FF'&gt;&lt;A href="markmap:loc=7023#813, -53203, -3631&amp;type=1&amp;name=Argonized Plants"&gt;argonized plants&lt;/A&gt;&lt;/font&gt; on the way. We have to get to the bottom of this or we'll all become argons.</Page>
    </Text>
    <Text id="15" huntingZoneId="245" prevId="14" villagerId="2009">
        <Page social="10">We've got everything we need. Let's begin.</Page>
    </Text>
    <Text id="16" huntingZoneId="245" villagerId="2009">
        <Page social="0">Argonized plants! I need samples of them!</Page>
    </Text>
    <Text id="17" huntingZoneId="245" prevId="15" villagerId="2009">
        <Page social="4">It's all about turning the argonomorphing frequencies back on themselves so that they dissipate. That's the theory anyway.&lt;BR&gt;&lt;BR&gt;So here's the potion. We'll try it on some poor argonomorph creature, and see what happens.</Page>
    </Text>
    <Text id="18" huntingZoneId="245" prevId="17" villagerId="2028">
        <Page social="1">(Use Ronalt's potion.)</Page>
    </Text>
    <Text id="19" huntingZoneId="245" prevId="18" villagerId="2009">
        <Page social="4">That's not exactly the expected result. Something is missing, but what? Take these research notes to Mitiel. He may spot my mistake.</Page>
    </Text>
    <Text id="20" huntingZoneId="245" prevId="19" villagerId="2002">
        <Page social="4">Even though the outcome wasn't what we hoped, Ronalt was on the right track. I think you should consult with Yasring and Rancien at the Three Towers.&lt;BR&gt;&lt;BR&gt;Take your questions, and Ronalt's research, to Rancien.</Page>
    </Text>
    <Text id="21" huntingZoneId="245" prevId="20" villagerId="2021">
        <Page social="4">Looking for {@creature:245#2001$$Yasring}? She left already. She's in the {@Rgn:46001$$Three Towers}.</Page>
    </Text>
</QuestDialog>
