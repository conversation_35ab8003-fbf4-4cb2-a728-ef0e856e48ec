<QuestDialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="https://vezel.dev/novadrop/dc/QuestDialog QuestDialog.xsd" id="41" huntingZoneId="32" voiceTypeId="0" xmlns="https://vezel.dev/novadrop/dc/QuestDialog">
    <Text id="1" huntingZoneId="0" prevId="0" villagerId="0" />
    <Text id="100" huntingZoneId="232" prevId="0" villagerId="1010">
        <Page social="0" />
    </Text>
    <Text id="2" huntingZoneId="232" prevId="0" villagerId="1010">
        <Page social="4">I volunteered for this posting because my sister died here. I hoped I might be able to retrieve her body.&lt;BR&gt;&lt;BR&gt;I never found it. She's still out there in the cold somewhere.&lt;NEXTPAGEBUTTON&gt;"I'm very sorry."&lt;/NEXTPAGEBUTTON&gt;</Page>
        <Page social="0">No need to worry about me. I'm a soldier. I know the risks. And, as long as I remember her, she's still alive in my heart.&lt;BR&gt;&lt;BR&gt;I make a point of leaving flowers for her at one of the graves of unknown soldiers near Acarum to keep her in my thoughts. If you go past the cemetery there, would you place these flowers for me?</Page>
    </Text>
    <Text id="3" huntingZoneId="73" prevId="2" villagerId="1114">
        <Page social="4">(The gravestone almost seems to thrum with the memories of lost friends, family, and fallen comrades.)</Page>
    </Text>
    <Text id="4" huntingZoneId="232" prevId="3" villagerId="1010">
        <Page social="4">Thank you. I just want her to know I remember.</Page>
    </Text>
</QuestDialog>
