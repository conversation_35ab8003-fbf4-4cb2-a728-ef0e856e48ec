<QuestDialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="https://vezel.dev/novadrop/dc/QuestDialog QuestDialog.xsd" id="15" huntingZoneId="123" voiceTypeId="0" xmlns="https://vezel.dev/novadrop/dc/QuestDialog">
    <Text id="1" huntingZoneId="0" prevId="0" villagerId="0" />
    <Text id="100" huntingZoneId="0" prevId="0" villagerId="0">
        <Page social="0" />
    </Text>
    <Text id="2" huntingZoneId="0" prevId="0" villagerId="0">
        <Page social="0">The enemy have entered Fort Dragonfall, and are tearing through our defenses. Hurry to Fort Dragonfall and help protect Executor Frateko!</Page>
    </Text>
    <Text id="3" huntingZoneId="123" prevId="2" villagerId="9001">
        <Page social="0">I'm glad my safety was in your hands, &lt;PCName&gt;.&lt;BR&gt;&lt;BR&gt;The Iron Order will remember your power!</Page>
    </Text>
</QuestDialog>
