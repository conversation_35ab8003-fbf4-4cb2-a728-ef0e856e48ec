<QuestDialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="https://vezel.dev/novadrop/dc/QuestDialog QuestDialog.xsd" id="40" huntingZoneId="32" voiceTypeId="0" xmlns="https://vezel.dev/novadrop/dc/QuestDialog">
    <Text id="1" huntingZoneId="0" prevId="0" villagerId="0" />
    <Text id="100" huntingZoneId="232" prevId="0" villagerId="1032">
        <Page social="0" />
    </Text>
    <Text id="2" huntingZoneId="232" prevId="0" villagerId="1032">
        <Page social="4">There's no word on reinforcements from {@Rgn:32008$$Holdouts Redoubt}.&lt;BR&gt;&lt;BR&gt;I understand it’s a difficult situation, but please go there and tell <PERSON><PERSON> we really need those troops.</Page>
    </Text>
    <Text id="3" huntingZoneId="232" prevId="2" villagerId="1002">
        <Page social="4">So, <PERSON><PERSON>'s short on manpower, eh? Well, so are we. Besides, I’m not in charge of troop management, <PERSON>wa is. See her if you have questions.</Page>
    </Text>
    <Text id="4" huntingZoneId="232" prevId="3" villagerId="1005">
        <Page social="4">Yeah, I heard Hallaj all the way over here.&lt;BR&gt;&lt;BR&gt;Sadly, he's right. We have no reinforccements we can send to Qale. I'm sorry you came all this way for bad news. I wish it could be different.</Page>
    </Text>
</QuestDialog>
