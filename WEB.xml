<?xml version="1.0" encoding="utf-8"?>
<Strings>
	<String ID="InvalidParam" CONTENT="Invalid input"/>
	<String ID="IsReservedUserName" CONTENT="The entered name is a reserved name and cannot be changed."/>
	<String ID="TotalServer" CONTENT="All Servers"/>
	<String ID="DeleteItem" CONTENT="Delete"/>
	<String ID="DeleteItemTooltip" CONTENT="Delete Selected"/>
	<String ID="DeleteList" CONTENT="Delete from list"/>
	<String ID="DeleteAsk" CONTENT="Are you sure you want to delete?"/>
	<String ID="DeleteSuccess" CONTENT="DeleteSuccess"/>
	<String ID="DeleteFail" CONTENT="Delete Failed"/>
	<String ID="DeleteResult" CONTENT="Delete success or not"/>
	<String ID="InsertItem" CONTENT="InsertItem"/>
	<String ID="InsertList" CONTENT="InsertList"/>
	<String ID="InsertAsk" CONTENT="Do you want to add?"/>
	<String ID="InsertSuccess" CONTENT="Added successfully"/>
	<String ID="InsertFail" CONTENT="InsertFail"/>
	<String ID="EditItem" CONTENT="Edit"/>
	<String ID="EditAsk" CONTENT="Are you sure you want to edit?"/>
	<String ID="EditSuccess" CONTENT="Modification successful"/>
	<String ID="EditFail" CONTENT="Edit failed"/>
	<String ID="CommaComment" CONTENT="Spaces can be entered at the same time"/>
	<String ID="ServerChoiceWarning" CONTENT="Be sure to select a server when finding a role"/>
	<String ID="ServerName" CONTENT="ServerName"/>
	<String ID="ServerNo" CONTENT="ServerNo"/>
	<String ID="ServerChoice" CONTENT="ServerChoice"/>
	<String ID="SearchResult" CONTENT="SearchResult"/>
	<String ID="Working" CONTENT="Working"/>
	<String ID="Send" CONTENT="Send"/>
	<String ID="Recv" CONTENT="Receive"/>
	<String ID="Submit" CONTENT="Submit"/>
	<String ID="SendTime" CONTENT="SendTime"/>
	<String ID="RecvTime" CONTENT="ReceiveTime"/>
	<String ID="PaidTime" CONTENT="PaymentTime"/>
	<String ID="Sender" CONTENT="Sender"/>
	<String ID="Recver" CONTENT="Recipient"/>
	<String ID="Msg" CONTENT="Message"/>
	<String ID="Type" CONTENT="Type"/>
	<String ID="Status" CONTENT="Status"/>
	<String ID="Location" CONTENT="Location"/>
	<String ID="Date" CONTENT="Time"/>
	<String ID="Write" CONTENT="Write"/>
	<String ID="Writer" CONTENT="Writer"/>
	<String ID="WriterAccount" CONTENT="Writer Account"/>
	<String ID="UserItemWarning" CONTENT="Please confirm whether the player information and item information are correct"/>
	<String ID="Cancel" CONTENT="Cancel"/>
	<String ID="OK" CONTENT="Ok"/>
	<String ID="Search" CONTENT="Search"/>
	<String ID="None" CONTENT="None"/>
	<String ID="RecentSearch" CONTENT="Recent Search"/>
	<String ID="RecentSearchAccount" CONTENT="Recently Searched Account"/>
	<String ID="Update" CONTENT="Update"/>
	<String ID="NotFound" CONTENT="Not Found"/>
	<String ID="Welcome" CONTENT=" Welcome!!"/>
	<String ID="ShowTotal" CONTENT="Show All"/>
	<String ID="Modify" CONTENT="Modify"/>
	<String ID="Content" CONTENT="Content"/>
	<String ID="Save" CONTENT="Save"/>
	<String ID="ReturnToList" CONTENT="ReturnToList"/>
	<String ID="UserNoteWarn1" CONTENT="Please enter member No"/>
	<String ID="UserNoteWarn2" CONTENT="Please enter the content of the sanctions"/>
	<String ID="UserNoteWarn3" CONTENT="Please enter character name"/>
	<String ID="UserNoteWarn4" CONTENT="Please enter content"/>
	<String ID="LogIn" CONTENT="LogIn"/>
	<String ID="LogOut" CONTENT="LogOut"/>
	<String ID="PCRoomPoint" CONTENT="PCRoomPoint"/>
	<String ID="DoAttach" CONTENT="Attach 3"/>
	<String ID="DoDelete" CONTENT="Delete Item"/>
	<String ID="DoEdit" CONTENT="Edit"/>
	<String ID="DoAdd" CONTENT="Add"/>
	<String ID="StopShop" CONTENT="Stop Shop"/>
	<String ID="Success" CONTENT="Success"/>
	<String ID="Fail" CONTENT="Fail"/>
	<String ID="DoSeizure" CONTENT="DoSeizure"/>
	<String ID="ReturnSeizure" CONTENT="ReturnSeizure"/>
	<String ID="VendingMachineDoSeizure" CONTENT="VendingMachineDoSeizure"/>
	<String ID="JustRequest" CONTENT="Approve Request"/>
	<String ID="Close" CONTENT="Close"/>
	<String ID="CloseToolTip" CONTENT="Close"/>
	<String ID="Request" CONTENT="Request"/>
	<String ID="Copy" CONTENT="Copy"/>
	<String ID="CopyServerChoice" CONTENT="Choose a server to copy"/>
	<String ID="CopyUserDBID" CONTENT="Copy User DBID"/>
	<String ID="Recovery" CONTENT="Recovery"/>
	<String ID="Rename" CONTENT="Rename"/>
	<String ID="InDelete" CONTENT="Waiting to delete"/>
	<String ID="DeleteComplete" CONTENT="Delete Complete"/>
	<String ID="MemoInputConfirm" CONTENT="Please enter information"/>
	<String ID="Teleport" CONTENT="Teleport"/>
	<String ID="TeleportPos" CONTENT="TeleportPos"/>
	<String ID="ConfirmTeleport" CONTENT="Please confirm the location of the character to be teleported"/>
	<String ID="UserLevel" CONTENT="User Level"/>
	<String ID="ChangeUserLevel" CONTENT="Change UserLevel"/>
	<String ID="ConfirmChangeUserLevel" CONTENT="Please confirm the user level"/>
	<String ID="ChangeUserPKPoint" CONTENT="Change the character PK value"/>
	<String ID="ConfirmChangeUserPKPoint" CONTENT="Please confirm to change the user PK value"/>
	<String ID="UndeleteUser" CONTENT="Recover deleted user"/>
	<String ID="ConfirmUndeleteUser" CONTENT="Confirm Undelete User"/>
	<String ID="ConfirmStopStore" CONTENT="Confirm Stop Store"/>
	<String ID="SeizureVendingItem" CONTENT="Personal Store Seizure"/>
	<String ID="DoSeizureVendingItem" CONTENT="Personal store seized"/>
	<String ID="ConfirmSeizureVendingItem" CONTENT="Please confirm the seizure of the personal store"/>
	<String ID="SeizureParcel" CONTENT="Parcel Seizure"/>
	<String ID="SeizureParcelInfo" CONTENT="Seizure Parcel Info"/>
	<String ID="ConfirmSeizureParcel" CONTENT="Please confirm the item to be seized"/>
	<String ID="SeizureMoney" CONTENT="Deposit Currency"/>
	<String ID="MoneyToSeizure" CONTENT="Gold Seizure"/>
	<String ID="SeizureMoneyInfo" CONTENT="Seizure Money Info"/>
	<String ID="ConfirmSeizureMoney" CONTENT="Please confirm the amount of gold coins to be seized"/>
	<String ID="SeizureItem" CONTENT="Seizure Item"/>
	<String ID="ConfirmSeizureItem" CONTENT="Please confirm the item to be seized"/>
	<String ID="SeizuredItemInfo" CONTENT="Seized item information"/>
	<String ID="ReturnSeizureMoney" CONTENT="Cancel Seizure"/>
	<String ID="ConfirmReturnSeizureMoney" CONTENT="Please confirm the release of the seized gold coins"/>
	<String ID="ConfirmDeleteSeizureMoney" CONTENT="Please confirm the deposit currency to delete"/>
	<String ID="ReturnedSeizureMoneyInfo" CONTENT="Returned SeizureMoneyInfo"/>
	<String ID="DeleteSeizureMoneyInfo" CONTENT="Delete SeizureMoneyInfo"/>
	<String ID="ReturnSeizureItem" CONTENT="Release Item Seizure"/>
	<String ID="DeleteSeizureItem" CONTENT="Delete SeizureItem"/>
	<String ID="ConfirmReturnSeizureItem" CONTENT="Please confirm the item to be released from the seizure"/>
	<String ID="ConfirmDeleteSeizureItem" CONTENT="Please confirm the Seizure Item to be deleted"/>
	<String ID="ReturnedSeizureItemInfo" CONTENT="Returned SeizureItemInfo"/>
	<String ID="DeleteSeizureItemInfo" CONTENT="Seizure item information to be deleted"/>
	<String ID="RenameUser" CONTENT="RenameUser"/>
	<String ID="NewName" CONTENT="NewName"/>
	<String ID="OldName" CONTENT="OldName"/>
	<String ID="RenameTime" CONTENT="RenameTime"/>
	<String ID="ConfirmRenameUser" CONTENT="Please confirm the name of the role to be changed"/>
	<String ID="EnterNewMoney" CONTENT="Enter the amount you want to change"/>
	<String ID="NewMoney" CONTENT="New Amount"/>
	<String ID="ChangeMoney" CONTENT="Change Money"/>
	<String ID="ConfirmChangeMoney" CONTENT="Please confirm the amount"/>
	<String ID="ChangedMoney" CONTENT="ChangedMoney"/>
	<String ID="DelItem" CONTENT="Delete Item"/>
	<String ID="ConfirmDelItem" CONTENT="Please confirm the item to be deleted"/>
	<String ID="DeledItemInfo" CONTENT="Deleted item information"/>
	<String ID="DelCrest" CONTENT="Delete Crest"/>
	<String ID="ConfirmDelCrest" CONTENT="ConfirmDelCrest"/>
	<String ID="DeledCrestInfo" CONTENT="DeledCrestInfo"/>
	<String ID="DelTradeBrokerItem" CONTENT="Delete Trade Broker Item"/>
	<String ID="TradeId" CONTENT="Trade ID"/>
	<String ID="ConfirmDelTradeBrokerItem" CONTENT="Please check if the item to be deleted is correct"/>
	<String ID="DeledTradeBrokerItemInfo" CONTENT="Deleted Trade Broker Item Info"/>
	<String ID="Result" CONTENT="Result"/>
	<String ID="Count" CONTENT="Count"/>
	<String ID="CountValue" CONTENT="CountValue"/>
	<String ID="Possible" CONTENT="Possible"/>
	<String ID="Impossible" CONTENT="Impossible"/>
	<String ID="Victory" CONTENT="Victory"/>
	<String ID="Defeat" CONTENT="Defeat"/>
	<String ID="Param" CONTENT="Parameter"/>
	<String ID="Requestor" CONTENT="Requestor"/>
	<String ID="Confirmor" CONTENT="Confirmor"/>
	<String ID="OpenDetail" CONTENT="Open Details"/>
	<String ID="CloseDetail" CONTENT="Close Details"/>
	<String ID="View" CONTENT="View"/>
	<String ID="Selected" CONTENT="Selected"/>
	<String ID="All" CONTENT="All"/>
	<String ID="ChangeRace" CONTENT="Change Race"/>
	<String ID="ChangeGender" CONTENT="Change Gender"/>
	<String ID="ChangeLooks" CONTENT="Change Look"/>
	<String ID="ChangeDate" CONTENT="Change Date"/>
	<String ID="List" CONTENT="List"/>
	<String ID="ClosedList" CONTENT="Close List"/>
	<String ID="No" CONTENT="No"/>
	<String ID="Name" CONTENT="Name"/>
	<String ID="Addr" CONTENT="Addr"/>
	<String ID="Port" CONTENT="Port"/>
	<String ID="PlanetId" CONTENT="PlanetId"/>
	<String ID="IsOnline" CONTENT="IsOnline"/>
	<String ID="UserLimit" CONTENT="UserLimit"/>
	<String ID="UpdateUserLimit" CONTENT="Update User Limit"/>
	<String ID="ReallyUpdate" CONTENT="Are you sure you want to change the online limit on this server?"/>
	<String ID="UpdateUserLimitWarning" CONTENT="Important: Changing the limit may greatly affect the service."/>
	<String ID="WorldDb" CONTENT="WorldDb"/>
	<String ID="LogDb" CONTENT="LogDb"/>
	<String ID="ReportDb" CONTENT="ReportDb"/>
	<String ID="MergeReportDb" CONTENT="MergeReportDb"/>
	<String ID="DeleteNotice" CONTENT="DeleteNotice"/>
	<String ID="SendNotice" CONTENT="SendNotice"/>
	<String ID="Notice" CONTENT="Notice"/>
	<String ID="ChatNotice" CONTENT="Chat Notice"/>
	<String ID="CenterNotice" CONTENT="Client Notice"/>
	<String ID="CenterNoticeInterval" CONTENT="Client Intermediate Notification Interval"/>
	<String ID="NoticePosition" CONTENT="NoticePosition"/>
	<String ID="NoticeInterval" CONTENT="NoticeInterval"/>
	<String ID="SendNoticeSuccess" CONTENT="{0}plus {1} server {2} sent notification successfully"/>
	<String ID="SendNoticeFail" CONTENT="{0}plus {1} server {2} failed to send notification"/>
	<String ID="AddNotice" CONTENT="Add Notice"/>
	<String ID="OldServer" CONTENT="Old Server"/>
	<String ID="NewServer" CONTENT="New Server"/>
	<String ID="MigrationTime" CONTENT="Migration Time"/>
	<String ID="Migration" CONTENT="Migration"/>
	<String ID="MigrationOutPossible" CONTENT="MigrationOutPossible"/>
	<String ID="MigrationInPossible" CONTENT="MigrationInPossible"/>
	<String ID="MigrationPossible" CONTENT="MigrationPossible"/>
	<String ID="MigrationImpossible" CONTENT="MigrationImpossible"/>
	<String ID="MigrationMinLevel" CONTENT="MigrationMinLevel"/>
	<String ID="MigrationMaxMoney" CONTENT="MigrationMaxMoney"/>
	<String ID="RefreshList" CONTENT="RefreshList"/>
	<String ID="TotalWaitUser" CONTENT="TotalWaitUser"/>
	<String ID="AverageConcurrentUser" CONTENT="AverageConcurrentUser"/>
	<String ID="MaxConcurrentUser" CONTENT="MaxConcurrentUser"/>
	<String ID="MinConcurrentUser" CONTENT="MinConcurrentUser"/>
	<String ID="TotalConcurrentUser" CONTENT="TotalConcurrentUser"/>
	<String ID="InternetCafeUser" CONTENT="InternetCafeUser"/>
	<String ID="ConcurrentUser" CONTENT="Online Player"/>
	<String ID="WaitCount" CONTENT="Waiting for connection"/>
	<String ID="NpcCount" CONTENT="NpcCount"/>
	<String ID="WorldStatus" CONTENT="WorldStatus"/>
	<String ID="LogStatus" CONTENT="LogStatus"/>
	<String ID="RestrictionStatus" CONTENT="Restriction Status"/>
	<String ID="InternetCafeConcurrent" CONTENT="Internet Cafe Connection"/>
	<String ID="ChangeMigrationSetting" CONTENT="Change Server Migration Settings"/>
	<String ID="ConfirmChangeMigrationSetting" CONTENT="Please confirm server configuration"/>
	<String ID="GM" CONTENT="GM"/>
	<String ID="AdminLev" CONTENT="Admin Level"/>
	<String ID="Restriction" CONTENT="Restriction"/>
	<String ID="RestrictionType" CONTENT="Restriction Type"/>
	<String ID="RestrictionContent" CONTENT="Restriction Content"/>
	<String ID="RestrictionInfo" CONTENT="Restriction Information"/>
	<String ID="NoChat" CONTENT="No Chat"/>
	<String ID="RestrictionAdd" CONTENT="Add Restriction"/>
	<String ID="CharRestrictionAdd" CONTENT="Add Character Restriction"/>
	<String ID="RestrictionToAdd" CONTENT="Additional restrictions"/>
	<String ID="ConfirmAddRestriction" CONTENT="Please confirm additional restrictions"/>
	<String ID="DeleteRestriction" CONTENT="Delete Restriction"/>
	<String ID="ConfirmDeleteRestriction" CONTENT="Please confirm the restriction to delete"/>
	<String ID="RestrictionToDelete" CONTENT="Restriction to delete"/>
	<String ID="DeletedRestrictionInfo" CONTENT="Deleted Restriction Info"/>
	<String ID="IsRescticted" CONTENT="IsRescticted"/>
	<String ID="SearchAccount" CONTENT="Account Search"/>
	<String ID="AccountDBID" CONTENT="AccountDBID"/>
	<String ID="AccountName" CONTENT="AccountName"/>
	<String ID="AccDBIDWarning" CONTENT="Please enter Account DBID"/>
	<String ID="AccNameWarning" CONTENT="Please enter an Account Number"/>
	<String ID="UserNameWarning" CONTENT="Please enter a user name"/>
	<String ID="WatchUser" CONTENT="WatchUser"/>
	<String ID="AccountInfo" CONTENT="AccountInfo"/>
	<String ID="CharHoldingSummary" CONTENT="CharHoldingSummary"/>
	<String ID="CharHolding" CONTENT="CharHolding"/>
	<String ID="DeletedCharHolding" CONTENT="DeletedCharHolding"/>
	<String ID="CharHoldingComment" CONTENT="Click [ServerName] in the following results to view the server's user information."/>
	<String ID="HoldingChar" CONTENT="HoldingChar"/>
	<String ID="DeleteChar" CONTENT="Deleted Character"/>
	<String ID="InDeleteChar" CONTENT="Character to be deleted"/>
	<String ID="TotChar" CONTENT="TotChart"/>
	<String ID="CharSlotTitle" CONTENT="CharServerSlotCount"/>
	<String ID="CharSlotCount" CONTENT="CharSlotCount"/>
	<String ID="CharSlotEdit" CONTENT="Edit"/>
	<String ID="CharSlotChange" CONTENT="CharSlotChange"/>
	<String ID="ConfirmCharSlotChange" CONTENT="ConfirmCharSlotChange"/>
	<String ID="AccFindFirst" CONTENT="Please click here to perform [Account Search] first"/>
	<String ID="SearchMemoByAccount" CONTENT="SearchMemoByAccount"/>
	<String ID="SearchMemoByChar" CONTENT="SearchMemoByChar"/>
	<String ID="SearchType" CONTENT="SearchType"/>
	<String ID="NoSearchType" CONTENT="Search object not found"/>
	<String ID="MemoLinkToAccount" CONTENT="MemoLinkToAccount: "/>
	<String ID="MemoLinkToChar" CONTENT="MemoLinkToChar: "/>
	<String ID="AccountShort" CONTENT="Account"/>
	<String ID="ChangeAppearanceType" CONTENT="Appearance Change Type"/>
	<String ID="OldRace" CONTENT="Race Change"/>
	<String ID="NewRace" CONTENT="New Race"/>
	<String ID="OldGender" CONTENT="Old Gender"/>
	<String ID="NewGender" CONTENT="New Gender"/>
	<String ID="OldLooks" CONTENT="Look before change"/>
	<String ID="NewLooks" CONTENT="New Look"/>
	<String ID="SearchMergeServer" CONTENT="SearchMergeServer"/>
	<String ID="OldServerName" CONTENT="Old Server"/>
	<String ID="NewServerName" CONTENT="New Server"/>
	<String ID="OldUserName" CONTENT="Old User name"/>
	<String ID="NewUserName" CONTENT="New User name"/>
	<String ID="NoMergeServer" CONTENT="NoMergeServer."/>
	<String ID="SearchByChat" CONTENT="SearchByChat"/>
	<String ID="SearchByIP" CONTENT="SearchByIP"/>
	<String ID="SearchByOperatorName" CONTENT="Search by operator name"/>
	<String ID="SearchUser" CONTENT="User Search"/>
	<String ID="UserInfo" CONTENT="UserInfo"/>
	<String ID="UserDBID" CONTENT="UserDBID"/>
	<String ID="UserName" CONTENT="UserName"/>
	<String ID="UserShort" CONTENT="UserName"/>
	<String ID="UserDetail" CONTENT="UserDetails"/>
	<String ID="UserTemplate" CONTENT="UserTemplate"/>
	<String ID="Gender" CONTENT="Gender"/>
	<String ID="Race" CONTENT="Race"/>
	<String ID="Class" CONTENT="Class"/>
	<String ID="Level" CONTENT="Level"/>
	<String ID="Exp" CONTENT="Exp."/>
	<String ID="Money" CONTENT="Money"/>
	<String ID="HP" CONTENT="HP"/>
	<String ID="MP" CONTENT="MP"/>
	<String ID="IsAlive" CONTENT="IsAlive"/>
	<String ID="PosX" CONTENT="PosX"/>
	<String ID="PosY" CONTENT="PosY"/>
	<String ID="PosZ" CONTENT="PosZ"/>
	<String ID="LastLogIn" CONTENT="LastLogIn"/>
	<String ID="LastLogOut" CONTENT="LastLogOut"/>
	<String ID="DeletionTime" CONTENT="DeletionTime"/>
	<String ID="DeleteRequestTime" CONTENT="DeleteRequestTime"/>
	<String ID="LastPos" CONTENT="LastPos"/>
	<String ID="LastLogoutPos" CONTENT="LastLogoutPos"/>
	<String ID="DelStatus" CONTENT="DelStatus"/>
	<String ID="DelTime" CONTENT="DelTime"/>
	<String ID="UserDBIDWarning" CONTENT="Please enter an User DBID"/>
	<String ID="User_UserNameWarning" CONTENT="Please enter an User Name"/>
	<String ID="PlayTime" CONTENT="PlayTime (minutes)"/>
	<String ID="Continent" CONTENT="Continent"/>
	<String ID="Channel" CONTENT="Channel"/>
	<String ID="Position" CONTENT="Position"/>
	<String ID="Pos" CONTENT="Position:Continent/Channel X,Y,Z]"/>
	<String ID="NickName" CONTENT="Title"/>
	<String ID="PK_PVP" CONTENT="PK/PVP"/>
	<String ID="UserNotFoundWarning" CONTENT="The corresponding user could not be found"/>
	<String ID="DeletionStatus" CONTENT="Deletion Status"/>
	<String ID="PartyLog" CONTENT="PartyLog"/>
	<String ID="PKLog" CONTENT="PKLog"/>
	<String ID="PvPLog" CONTENT="PvPLog"/>
	<String ID="ViewUserMemo" CONTENT="ViewUserMemo"/>
	<String ID="ViewAccountMemo" CONTENT="ViewAccountMemo"/>
	<String ID="DeleteSkill" CONTENT="DeleteSkill"/>
	<String ID="AddSkill" CONTENT="AddSkill"/>
	<String ID="UserRestrict" CONTENT="UserRestriction"/>
	<String ID="UserRestrictDbId" CONTENT="UserRestrictDbId"/>
	<String ID="RestrictLogin" CONTENT="RestrictLogin"/>
	<String ID="RestrictVendingMachine" CONTENT="RestrictVendingMachine"/>
	<String ID="RestrictAccountChatBan" CONTENT="BanAccountChat"/>
	<String ID="RestrictIn" CONTENT="RestrictIn"/>
	<String ID="RestrictEnded" CONTENT="RestrictEnded"/>
	<String ID="CurrentStatUpdateTime" CONTENT="Current Status Update Time"/>
	<String ID="currentRegionId" CONTENT="Current RegionId"/>
	<String ID="currentHPMPSTAMINA" CONTENT="Current HP/MP/STAMINA"/>
	<String ID="currentMaxAttack" CONTENT="Attack"/>
	<String ID="currentDefence" CONTENT="Defence"/>
	<String ID="currentAttackSpeed" CONTENT="Attack Speed"/>
	<String ID="currentRunSpeed" CONTENT="Movement Speed"/>
	<String ID="currentAtk" CONTENT="Power"/>
	<String ID="currentDef" CONTENT="Endurance"/>
	<String ID="currentReactionStr" CONTENT="Magic Resistance"/>
	<String ID="currentReactionRegist" CONTENT="Physical Resistance"/>
	<String ID="currentCriticalStr" CONTENT="Critical Trigger"/>
	<String ID="currentCriticalRegist" CONTENT="Critical Strike"/>
	<String ID="currentCriticalPowerRate" CONTENT="Critical PowerRate"/>
	<String ID="currentImpact" CONTENT="Impact"/>
	<String ID="currentBalance" CONTENT="Balance"/>
	<String ID="currentAbnormalARes" CONTENT="Status Weakening Resistance"/>
	<String ID="currentAbnormalBRes" CONTENT="Continuous Damage Resistance"/>
	<String ID="currentAbnormalCRes" CONTENT="Cannot Resist"/>
	<String ID="CreateDate" CONTENT="CreateDate"/>
	<String ID="EarnExpDelta" CONTENT="EarnExpDelta"/>
	<String ID="EarnExpResult" CONTENT="EarnExpResult"/>
	<String ID="BookmarkId" CONTENT="BookmarkId"/>
	<String ID="BookmarkName" CONTENT="BookmarkName"/>
	<String ID="BookmarkWorld" CONTENT="BookmarkWorld"/>
	<String ID="BookmarkContinent" CONTENT="Bookmark Continent"/>
	<String ID="BookmarkPosX" CONTENT="Pos X"/>
	<String ID="BookmarkPosY" CONTENT="Pos Y"/>
	<String ID="BookmarkPosZ" CONTENT="Pos Z"/>
	<String ID="BookmarkCopied" CONTENT="{0} skill books have been copied."/>
	<String ID="BookmarkCopied2" CONTENT="Only re-login this role can apply to the list of skill books."/>
	<String ID="PvpLoseTime" CONTENT="PvPLostTime"/>
	<String ID="PvpWinTime" CONTENT="PvPWinTime"/>
	<String ID="PkLoseTime" CONTENT="PKLoseTime"/>
	<String ID="PkWinTime" CONTENT="PKWinTime"/>
	<String ID="OpponentChar" CONTENT="Opponent Character"/>
	<String ID="PkPoint" CONTENT="PkPoint"/>
	<String ID="PartyTime" CONTENT="PartyTime"/>
	<String ID="PartyCount" CONTENT="PartyCount"/>
	<String ID="ChangeExp" CONTENT="EXPChange"/>
	<String ID="ConfirmChangeExp" CONTENT="Please confirm character experience value"/>
	<String ID="ConfirmClearClientSetting" CONTENT="User DBID Confirm"/>
	<String ID="DisableTerritory" CONTENT="DisableTerritory"/>
	<String ID="SetRespawnTime" CONTENT="Set Respawn Time"/>
	<String ID="HuntingZoneId" CONTENT="HuntingZoneId"/>
	<String ID="TerritoryId" CONTENT="TerritoryId"/>
	<String ID="TemplateId" CONTENT="TemplateId"/>
	<String ID="PartyId" CONTENT="PartyId"/>
	<String ID="RequestId" CONTENT="RequestId"/>
	<String ID="Set" CONTENT="Set"/>
	<String ID="Unset" CONTENT="Unset"/>
	<String ID="Npc_All" CONTENT="Overall"/>
	<String ID="RespawnTime" CONTENT="RespawnTime"/>
	<String ID="RespawnRequest" CONTENT="RespawnRequest"/>
	<String ID="InstanceId" CONTENT="InstanceId"/>
	<String ID="Dungeon" CONTENT="Dungeon"/>
	<String ID="DungeonCooltimeReset" CONTENT="Dungeon Cooldown Time"/>
	<String ID="GOUserDbId" CONTENT="GO User DBID"/>
	<String ID="FieldNpcRespawnDesc" CONTENT="The channel of the GO player who entered the DBID will have monsters refreshed."/>
	<String ID="HuntingZoneChoice" CONTENT="HuntingZoneChoice"/>
	<String ID="SpawningNpc" CONTENT="Spawning Npc"/>
	<String ID="HuntingZoneName" CONTENT="HuntingZoneName"/>
	<String ID="ClearRate" CONTENT="ClearRate"/>
	<String ID="ConfirmDungeonInfo" CONTENT="Please confirm the Dungeon information"/>
	<String ID="DungeonNpcRespawn" CONTENT="Dungeon/Scene NPC Respawn"/>
	<String ID="SelectNpcToRespawn" CONTENT="Please select an NPC to respawn"/>
	<String ID="ConfirmNpcToRespawn" CONTENT="Please confirm the NPC to respawn"/>
	<String ID="DungeonNpcRespawnResult" CONTENT="Dungeon NPC Respawn Application Result"/>
	<String ID="StopSpawn" CONTENT="Stop"/>
	<String ID="ConfirmStopSpawn" CONTENT="Please confirm stop spawn"/>
	<String ID="StopSpawnResult" CONTENT="Stop spawn request result"/>
	<String ID="ChangeRespawnTime" CONTENT="Change Respawn Time"/>
	<String ID="ConfirmChangeRespawnTime" CONTENT="Please confirm the respawn time setting"/>
	<String ID="ChangeRespawnTimeResult" CONTENT="Respawn time change request result"/>
	<String ID="EquipItem" CONTENT="EquipItem"/>
	<String ID="Inven" CONTENT="Backpack"/>
	<String ID="InvenType" CONTENT="InvenType"/>
	<String ID="ItemInfo" CONTENT="ItemInfo"/>
	<String ID="ItemName" CONTENT="ItemName"/>
	<String ID="ItemDBID" CONTENT="ItemDBID"/>
	<String ID="ItemID" CONTENT="ItemID"/>
	<String ID="ItemLevel" CONTENT="ItemLevel"/>
	<String ID="Stack" CONTENT="Stack"/>
	<String ID="Enchant" CONTENT="Enchant"/>
	<String ID="Custom" CONTENT="Custom"/>
	<String ID="CustomCount" CONTENT="Number of crystals"/>
	<String ID="Custom0" CONTENT="Crystal 0"/>
	<String ID="Custom1" CONTENT="Crystal 1"/>
	<String ID="Custom2" CONTENT="Crystal 2"/>
	<String ID="Custom3" CONTENT="Crystal 3"/>
	<String ID="Custom4" CONTENT="Crystal 4"/>
	<String ID="SearchItem" CONTENT="SearchItem"/>
	<String ID="SearchItemID" CONTENT="SearchItemID"/>
	<String ID="SearchItemDBID" CONTENT="SearchItemDBID"/>
	<String ID="Owner" CONTENT="Owner"/>
	<String ID="OwnerInfo" CONTENT="OwnerInfo"/>
	<String ID="OwnerDBID" CONTENT="OwnerDBID"/>
	<String ID="OwnerName" CONTENT="OwnerName (Account/Family/Guild)"/>
	<String ID="ItemDelete" CONTENT="DeleteItem"/>
	<String ID="ItemInsert" CONTENT="AddItem"/>
	<String ID="Item_Pos" CONTENT="ItemPos"/>
	<String ID="WareHouse" CONTENT="Warehouse"/>
	<String ID="Item_NotFound" CONTENT="The item could not be found"/>
	<String ID="ItemConfirmDel" CONTENT="Please confirm if the amount and item are correct."/>
	<String ID="ItemConfirmAdd" CONTENT="Please confirm if the amount and item are correct."/>
	<String ID="Item_DeleteSuccess" CONTENT="Delete success or not"/>
	<String ID="SeizureInven" CONTENT="Backpack Seizure Item"/>
	<String ID="SeizureWarehouse" CONTENT="Warehouse Seizure Items"/>
	<String ID="SeizureGuildWarehouse" CONTENT="Guild Warehouse Seizure Items"/>
	<String ID="SeizureUserWarehouse" CONTENT="Personal safe deposit box seizure item"/>
	<String ID="SeizureSell" CONTENT="SeizureSell Vending Machine"/>
	<String ID="SeizureBuy" CONTENT="Seizure of vending machines"/>
	<String ID="MoneyDelta" CONTENT="Money Delta"/>
	<String ID="MoneyResult" CONTENT="Result of money amount"/>
	<String ID="ItemValue" CONTENT="ItemValue"/>
	<String ID="TradeBrokerId" CONTENT="TradeBrokerId"/>
	<String ID="SeizureMoneyId" CONTENT="Seizure Number"/>
	<String ID="Item_SeizureMoney" CONTENT="Seizure Amount"/>
	<String ID="SeizureTime" CONTENT="Seizure Time"/>
	<String ID="SeizureMoneyTitle" CONTENT="Backpack deduction currency"/>
	<String ID="DeleteAmount" CONTENT="DeleteAmount"/>
	<String ID="ItemSealed" CONTENT="ItemSealed"/>
	<String ID="ItemNotSealed" CONTENT="ItemNotSealed"/>
	<String ID="DetailInfo" CONTENT="DetailInfo"/>
	<String ID="EnchantScrollInfo" CONTENT="Information scroll content"/>
	<String ID="SealedUserDBID" CONTENT="SealedUserDBID"/>
	<String ID="ProducerName" CONTENT="ProducerName"/>
	<String ID="Parcel" CONTENT="Parcel"/>
	<String ID="DeletedItem" CONTENT="Confirm delete item"/>
	<String ID="SearchDeletedItem" CONTENT="Search Deleted Item"/>
	<String ID="AmountDelta" CONTENT="Amount Delta"/>
	<String ID="AmountResult" CONTENT="Amount Result"/>
	<String ID="RandomPassive" CONTENT="Random passive effect"/>
	<String ID="RandomEnchantPassive" CONTENT="Random Enhancing Effect"/>
	<String ID="EnchantScrollPassive" CONTENT="Enchanted Effect"/>
	<String ID="Item_DeletionTime" CONTENT="Deletion Time"/>
	<String ID="Random" CONTENT="Random Effect"/>
	<String ID="IndPrice" CONTENT="Unit Price"/>
	<String ID="IndTax" CONTENT="Individual Tax"/>
	<String ID="Exterior" CONTENT="Change appearance"/>
	<String ID="Coloring" CONTENT="Coloring"/>
	<String ID="OldExterior" CONTENT="Change the original appearance"/>
	<String ID="OldColoring" CONTENT="Old Coloring"/>
	<String ID="ResetExterior" CONTENT="Initialize Exterior Change"/>
	<String ID="ResetColoring" CONTENT="Reset Coloring"/>
	<String ID="ConfirmResetExterior" CONTENT="Please confirm the shape change initialization item"/>
	<String ID="ConfirmResetColoring" CONTENT="Please confirm the props for hair coloring initialization"/>
	<String ID="LeftColoringTime" CONTENT="Left Coloring Time"/>
	<String ID="ColoringStartTime" CONTENT="Period Limit Coloring Start Time"/>
	<String ID="ColoringEndTime" CONTENT="Period limit coloring end time"/>
	<String ID="RareGrade" CONTENT="RareGrade"/>
	<String ID="Masterpiece" CONTENT="Masterpiece"/>
	<String ID="MasterpieceRevise" CONTENT="Masterpiece Revise"/>
	<String ID="UnidentifiedGrade" CONTENT="Seal Grade"/>
	<String ID="UnidentifiedGradeNone" CONTENT="UnidentifiedGradeNone"/>
	<String ID="CouponID" CONTENT="CouponID"/>
	<String ID="SearchCashItemLog" CONTENT="Search Charge Item Log"/>
	<String ID="CASHITEMLOG_NEW" CONTENT="Get it from the Goddess' Gift Pack"/>
	<String ID="CASHITEMLOG_DELETE" CONTENT="Destruction"/>
	<String ID="CASHITEMLOG_USE" CONTENT="Use"/>
	<String ID="CASHITEMLOG_CHANGE_OWNER" CONTENT="All Changes"/>
	<String ID="CASHITEMLOG_CHANGE_INVEN" CONTENT="Change"/>
	<String ID="CASHITEMLOG_CHANGE_AMOUNT" CONTENT="Change quantity"/>
	<String ID="RecvParcel" CONTENT="Receipt"/>
	<String ID="SendParcel" CONTENT="Send"/>
	<String ID="Parcel_Sender" CONTENT="Sender"/>
	<String ID="Parcel_Recver" CONTENT="Recipient"/>
	<String ID="ParcelDBID" CONTENT="ParcelDBID"/>
	<String ID="Parcel_Type" CONTENT="Type"/>
	<String ID="Parcel_Status" CONTENT="Status"/>
	<String ID="Parcel_SendTime" CONTENT="Send Time"/>
	<String ID="Parcel_RecvTime" CONTENT="Receipt completion time"/>
	<String ID="Postage" CONTENT="Postage"/>
	<String ID="Parcel_ItemName" CONTENT="Item name"/>
	<String ID="ItemDbId" CONTENT="Item DBID"/>
	<String ID="ItemAmount" CONTENT="Item Amount"/>
	<String ID="SendMoney" CONTENT="Send Money"/>
	<String ID="EscrowMoney" CONTENT="Request Amount"/>
	<String ID="Title" CONTENT="Title"/>
	<String ID="Parcel_Msg" CONTENT="Message"/>
	<String ID="UNREAD" CONTENT="Unread"/>
	<String ID="CAN_GET" CONTENT="Waiting to receive"/>
	<String ID="GOT" CONTENT="Collection completed"/>
	<String ID="DELETED" CONTENT="Deleted"/>
	<String ID="SEIZURE" CONTENT="Detained"/>
	<String ID="NORMAL" CONTENT="Normal"/>
	<String ID="PRESENT" CONTENT="Gift of Name"/>
	<String ID="ESCROW" CONTENT="Transaction"/>
	<String ID="SYS_NORMAL" CONTENT="S Normal"/>
	<String ID="SYS_PRESENT" CONTENT="S name gift"/>
	<String ID="SYS_ESCROW_RETURN" CONTENT="S Return Transaction"/>
	<String ID="SYS_ESCROW_ACCEPT" CONTENT="S Accept Transaction"/>
	<String ID="TitleContent" CONTENT="Title/Content"/>
	<String ID="VendingItem" CONTENT="VendingItem"/>
	<String ID="SellPrice" CONTENT="SellPrice"/>
	<String ID="BuyPrice" CONTENT="BuyPrice"/>
	<String ID="ADMsg" CONTENT="Store Promotion"/>
	<String ID="registerCount" CONTENT="RegisterCount"/>
	<String ID="tradeCount" CONTENT="TradeCount"/>
	<String ID="itemPrice" CONTENT="ItemPrice"/>
	<String ID="VT_SELLING" CONTENT="Selling"/>
	<String ID="VT_BUYING" CONTENT="Buy"/>
	<String ID="Seller" CONTENT="Seller"/>
	<String ID="Buyer" CONTENT="Buyer"/>
	<String ID="HomoonBasicInfo" CONTENT="Basic Info"/>
	<String ID="petItemTemplateId" CONTENT="pet item"/>
	<String ID="durability" CONTENT="durability"/>
	<String ID="orbItemTemplateId0" CONTENT="Object 0"/>
	<String ID="orbItemTemplateId1" CONTENT="Object 1"/>
	<String ID="orbItemTemplateId2" CONTENT="Object 2"/>
	<String ID="orbItemTemplateId3" CONTENT="Object 3"/>
	<String ID="orbItemTemplateId4" CONTENT="Object 4"/>
	<String ID="BuySellStatus" CONTENT="Store Status"/>
	<String ID="BuySellStatusSell" CONTENT="Sale Period"/>
	<String ID="BuySellStatusBuy" CONTENT="Buy"/>
	<String ID="BuySellStatusNone" CONTENT="BuySellStatusNone"/>
	<String ID="VendingMachineMsg" CONTENT="Vending Machine Statement"/>
	<String ID="VMMoney" CONTENT="Vending Machine"/>
	<String ID="AgitInfo" CONTENT="Shelter information"/>
	<String ID="AgitDBID" CONTENT="DBID shelter"/>
	<String ID="GetDate" CONTENT="Minimum Holding Time"/>
	<String ID="LastPaidTime" CONTENT="Time of last payment for ownership"/>
	<String ID="RentFailCount" CONTENT="Number of rental fees not paid"/>
	<String ID="IsSelling" CONTENT="Ownership Selling Status"/>
	<String ID="SellingPrice" CONTENT="Selling Price"/>
	<String ID="SellComment" CONTENT="Sale Announcement"/>
	<String ID="SellBaseTime" CONTENT="Sell Registration Time"/>
	<String ID="UseUntilTime" CONTENT="Time limit for use during sale"/>
	<String ID="SearchGuild" CONTENT="Guild Search"/>
	<String ID="GuildInfo" CONTENT="Guild Info"/>
	<String ID="GuildDBID" CONTENT="Guild DBID"/>
	<String ID="GuildName" CONTENT="Guild Name"/>
	<String ID="MemberCount" CONTENT="Guild Member"/>
	<String ID="Chief" CONTENT="Guild Chief"/>
	<String ID="Guild_Money" CONTENT="Guild Money"/>
	<String ID="Guild_CreateDate" CONTENT="Guild Creation Date"/>
	<String ID="Guild_DestroyDate" CONTENT="Guild Destroy Date"/>
	<String ID="BattleCount" CONTENT="Battle Victory/Loss/Escape"/>
	<String ID="WarPoint" CONTENT="War Point"/>
	<String ID="GuildMember" CONTENT="GuildMember Information"/>
	<String ID="GuildGroup" CONTENT="Guild Group"/>
	<String ID="GuildGroupName" CONTENT="Class Name"/>
	<String ID="GuildGroupID" CONTENT="Guild ID"/>
	<String ID="GuildAuth" CONTENT="Guild Authorization"/>
	<String ID="Guild_WareHouse" CONTENT="Guild Warehouse"/>
	<String ID="GuildFindFirst" CONTENT="Please click here to search for [Guild] first"/>
	<String ID="GuildLev" CONTENT="GuildLev"/>
	<String ID="GuildPayDate" CONTENT="Guild Rank Maintenance Fee Payment Date"/>
	<String ID="GuildLevelExpire" CONTENT="Guild Level Expire Period"/>
	<String ID="GuildAnnounce" CONTENT="Guild Announcement"/>
	<String ID="GA_INVITE" CONTENT="Join Processing"/>
	<String ID="GA_WAREHOUSE" CONTENT="Warehouse Management"/>
	<String ID="GA_GUILD_NOTICE" CONTENT="Guild Notice"/>
	<String ID="GA_ALL" CONTENT="All permissions"/>
	<String ID="GuildTitle" CONTENT="Guild Title"/>
	<String ID="ChangeGuildLevelExpire" CONTENT="Guild level maintenance period change"/>
	<String ID="ConfirmChangeGuildLevelExpire" CONTENT="Please confirm the guild level maintenance period to be changed"/>
	<String ID="ConfirmChangeGuildLevelExpire2" CONTENT="The maintenance period is the same as 20100507 1100, only the time unit can be changed"/>
	<String ID="ChangeGuildAnnounce" CONTENT="Change Guild Announcement"/>
	<String ID="ConfirmChangeGuildAnnounce" CONTENT="Please confirm the Guild Announcement to be changed"/>
	<String ID="ChangeGuildChief" CONTENT="Change GuildChief"/>
	<String ID="ConfirmChangeGuildChief" CONTENT="Please confirm the name of the Guild Chief to be changed"/>
	<String ID="ChangeGuildName" CONTENT="Change GuildName"/>
	<String ID="ConfirmChangeGuildName" CONTENT="Please confirm the guild name to change"/>
	<String ID="ChangeGuildTitle" CONTENT="Change GuildTitle"/>
	<String ID="ConfirmChangeGuildTitle" CONTENT="Please confirm the guild title to change"/>
	<String ID="ChangeGuildLevel" CONTENT="Change Guild Level"/>
	<String ID="ConfirmChangeGuildLevel" CONTENT="Please confirm the guild level to change"/>
	<String ID="ChangeGuildBattleChip" CONTENT="Guild BattleChip Change"/>
	<String ID="ConfirmChangeGuildBattleChip" CONTENT="Please confirm to change Guild BattleChip"/>
	<String ID="DeleteGuild" CONTENT="Delete Guild"/>
	<String ID="ConfirmDeleteGuild" CONTENT="Please confirm the name of the guild to delete"/>
	<String ID="DeleteGuildLog" CONTENT="Delete GuildLog"/>
	<String ID="ConfirmDeleteGuildLog" CONTENT="Please confirm the name of the guild whose logo you want to delete"/>
	<String ID="GuildWar" CONTENT="GuildWar Information"/>
	<String ID="GuildWarConfig" CONTENT="Guild War Settings"/>
	<String ID="GuildWarOnOff" CONTENT="Whether to conduct Guild War"/>
	<String ID="GuildWarControl" CONTENT="GuildWarControl"/>
	<String ID="GuildBattleChipControl" CONTENT="Tactical Chip Control"/>
	<String ID="PayGuildBattleChip" CONTENT="One-time payment tactical chip"/>
	<String ID="ClearGuildBattleChip" CONTENT="Batch Recovery Tactical Chip"/>
	<String ID="GuildWar_on" CONTENT="GuildWar On"/>
	<String ID="GuildWar_off" CONTENT="GuildWar Off"/>
	<String ID="GuildWar_enable" CONTENT="Enable"/>
	<String ID="GuildWar_disable" CONTENT="Cannot proceed"/>
	<String ID="GuildWarId" CONTENT="GuildWarId"/>
	<String ID="GuildWar_FromGuildId" CONTENT="Release Guild ID"/>
	<String ID="GuildWar_FromGuildName" CONTENT="Release Guild Name"/>
	<String ID="GuildWar_ToGuildId" CONTENT="Declared War Guild ID"/>
	<String ID="GuildWar_ToGuildName" CONTENT="Name of the declared war guild"/>
	<String ID="GuildWar_RaiseGuildName" CONTENT="Raise Bet Guild"/>
	<String ID="GuildWar_OpponentGuildName" CONTENT="Opponent Guild"/>
	<String ID="GuildWar_StartTime" CONTENT="GuildWar start time"/>
	<String ID="GuildWar_EndTime" CONTENT="GuildWar End Time"/>
	<String ID="GuildWar_FromGuildPoint" CONTENT="Release Guild Points"/>
	<String ID="GuildWar_ToGuildPoint" CONTENT="Guild Points Declared War"/>
	<String ID="GuildWar_DeclareCount" CONTENT="Number of Releases"/>
	<String ID="GuildWar_EncounterCount" CONTENT="Number of wars declared"/>
	<String ID="GuildWar_BettingAmount" CONTENT="Betting Amount"/>
	<String ID="GuildWar_Result" CONTENT="Result"/>
	<String ID="GuildWar_Result_FromGuildWin" CONTENT="Declared War Victory"/>
	<String ID="GuildWar_Result_ToGuildWin" CONTENT="The declared war guild wins"/>
	<String ID="GuildWar_Result_FromGuildGiveUp" CONTENT="Guild War Guild Give Up"/>
	<String ID="GuildWar_Result_ToGuildGiveUp" CONTENT="Give up by the declared war guild"/>
	<String ID="GuildWar_Result_FromGuildSystemCancel" CONTENT="Declare War Guild Tactical Chip Insufficient Cancel"/>
	<String ID="GuildWar_Result_ToGuildSystemCancel" CONTENT="Canceled by insufficient tactical chips of the declared war guild"/>
	<String ID="GuildWar_Result_DeclareCancel" CONTENT="Cancel declaration of war"/>
	<String ID="GuildWar_Result_RaiseCancel" CONTENT="Cancel Raise Bet"/>
	<String ID="GuildWar_Result_FromGuildSurrender" CONTENT="Guild War Surrender"/>
	<String ID="GuildWar_Result_ToGuildSurrender" CONTENT="Guild War Surrendered"/>
	<String ID="GuildWar_Result_Draw" CONTENT="Tie"/>
	<String ID="GuildWar_Result_Doing" CONTENT="In Progress"/>
	<String ID="GuildWar_Result_SystemCancel" CONTENT="Cancel by System"/>
	<String ID="GuildWar_Result_GuildCompetition_End" CONTENT="Cancel after Guild Competition"/>
	<String ID="GuildGeneralCoin_BattleChip" CONTENT="Tactical Chip"/>
	<String ID="GuildGeneralCoin_FloatingCastle" CONTENT="Abundance Coin"/>
	<String ID="GuildGeneralCoin_BattleChipAmount" CONTENT="Tactical chip possession"/>
	<String ID="NewGuildWarPoint" CONTENT="New GuildWarPoint"/>
	<String ID="ConfirmNewGuildWarPoint" CONTENT="Please confirm the Guild War Point to change"/>
	<String ID="ChangeGuildWarPoint" CONTENT="Change GuildWarPoint"/>
	<String ID="OpponentGuildInfo" CONTENT="Opponent Guild Information"/>
	<String ID="OpponentGuildDBID" CONTENT="Opponent Guild DBID"/>
	<String ID="LT_GUILD_WAR_BEGIN" CONTENT="Start Guild War"/>
	<String ID="LT_GUILD_WAR_END" CONTENT="End of Guild War"/>
	<String ID="LT_GUILD_WAR_OBTAIN_POINT" CONTENT="Get Guild War Points"/>
	<String ID="LT_GUILD_WAR_DECLARE" CONTENT="Declaration of Guild War"/>
	<String ID="LT_GUILD_WAR_RAISE" CONTENT="Guild War Raise Bet"/>
	<String ID="LT_GUILD_WAR_ENCOUNTER" CONTENT="Announcing gang war"/>
	<String ID="LT_LORD_ELECTION_GUILD_WAR_OBTAIN_POINT" CONTENT="Obtain lord selection gang battle points"/>
	<String ID="LT_GUILD_TYPE" CONTENT="Guild Type"/>
	<String ID="LT_GUILD_TYPE_NORMAL" CONTENT="Normal Guild"/>
	<String ID="LT_GUILD_TYPE_SYSTEM" CONTENT="System Guild"/>
	<String ID="LT_UPDATE_GUILD_BATTLE_CHIP" CONTENT="Guild Tactical Chip Changes"/>
	<String ID="LT_UPDATE_GUILD_FLOATING_CASTLE_COIN" CONTENT="Abundance Coin Change"/>
	<String ID="REQUEST_BANISH_GUILD_MEMBER" CONTENT="Expel Guild Members"/>
	<String ID="Banish_Guild_Member" CONTENT="Banish Guild"/>
	<String ID="ConfirmBanishGuildMember" CONTENT="Are you sure you want to expel gang members?"/>
	<String ID="RestBonus" CONTENT="Rest point"/>
	<String ID="SetRestBonus" CONTENT="Set rest point"/>
	<String ID="ConfirmSetRestBonus" CONTENT="Please confirm the rest point"/>
	<String ID="ConfirmAttendance" CONTENT="Please confirm attendance"/>
	<String ID="MemoStr" CONTENT="Please enter content"/>
	<String ID="MemoList" CONTENT="Record List"/>
	<String ID="MemoDBID" CONTENT="Memo DBID"/>
	<String ID="MemoContent" CONTENT="Memo Content"/>
	<String ID="MemoWirte" CONTENT="Record"/>
	<String ID="MemoAdd" CONTENT="Add Memo"/>
	<String ID="MemoMgr" CONTENT="Record Management"/>
	<String ID="MemoLast" CONTENT="Last Message"/>
	<String ID="MemoDetail" CONTENT="Content of this article"/>
	<String ID="MemoRegister" CONTENT="Message"/>
	<String ID="RelateRequest" CONTENT="Related Billing"/>
	<String ID="SkillList" CONTENT="Active Skill List"/>
	<String ID="PassiveSkillList" CONTENT="Passive Skill List"/>
	<String ID="SkillID" CONTENT="Skill ID"/>
	<String ID="SkillName" CONTENT="SkillName"/>
	<String ID="SkillConfirmDel" CONTENT="Please confirm whether the skill deletion object and skill information are correct"/>
	<String ID="SkillAdd" CONTENT="Please select a skill to add"/>
	<String ID="SkillConfirmAdd" CONTENT="Please confirm whether the added skill information is correct"/>
	<String ID="InsertSkill" CONTENT="InsertSkill"/>
	<String ID="Skill_DeleteSkill" CONTENT="Delete Skill"/>
	<String ID="DeleteSkillList" CONTENT="DeleteSkillList"/>
	<String ID="SkillToInsert" CONTENT="SkillToInsert"/>
	<String ID="CompleteQuestList" CONTENT="CompleteQuestList"/>
	<String ID="ProgressQuestList" CONTENT="ProgressQuestList"/>
	<String ID="StopQuestList" CONTENT="StopQuest List"/>
	<String ID="SearchQuest" CONTENT="SearchQuest"/>
	<String ID="QuestInfo" CONTENT="QuestInfo"/>
	<String ID="QuestDBID" CONTENT="QuestDBID"/>
	<String ID="QuestID" CONTENT="QuestID"/>
	<String ID="QuestName" CONTENT="QuestName"/>
	<String ID="QuestType" CONTENT="QuestType"/>
	<String ID="State" CONTENT="State"/>
	<String ID="TaskStep" CONTENT="Progress stage"/>
	<String ID="TimeOut" CONTENT="TimeOut"/>
	<String ID="Repeat" CONTENT="Repeat"/>
	<String ID="Quest_Cancle" CONTENT="CanCancel"/>
	<String ID="MinLev" CONTENT="MinLev"/>
	<String ID="MaxLev" CONTENT="MaxLev"/>
	<String ID="PreQuest" CONTENT="PreQuest"/>
	<String ID="Goal" CONTENT="Goal"/>
	<String ID="FirstTask" CONTENT="FirstTask"/>
	<String ID="QuestFindFirst" CONTENT="Click Here to Start [Quest Search]"/>
	<String ID="TaskID" CONTENT="TaskID"/>
	<String ID="TaskType" CONTENT="TaskType"/>
	<String ID="TaskContent" CONTENT="TaskContent"/>
	<String ID="RepeatNumber" CONTENT="RepeatNumber"/>
	<String ID="CompleteTime" CONTENT="QuestCompletionDate"/>
	<String ID="DeleteQuest" CONTENT="DeleteQuest"/>
	<String ID="AddQuest" CONTENT="AddQuest"/>
	<String ID="EditQuest" CONTENT="EditQuest"/>
	<String ID="QuestConfirmDel" CONTENT="Please confirm whether the task deletion object and task information are correct"/>
	<String ID="QuestConfirmAdd" CONTENT="Please confirm whether the quest add object and quest information are correct"/>
	<String ID="QuestConfirmEdit" CONTENT="Please confirm whether the quest editing object and quest information are correct"/>
	<String ID="QuestExist" CONTENT="Quest in Progress"/>
	<String ID="KeepItem" CONTENT="Warehouse Keeping Items"/>
	<String ID="PayTime" CONTENT="The date on which the fee is scheduled to be paid"/>
	<String ID="WareHouse_Money" CONTENT="WarehouseMoney"/>
	<String ID="CommTime" CONTENT="Payment commission time"/>
	<String ID="ChangeWarehouseMoney" CONTENT="Modify the amount of gold coins in the warehouse"/>
	<String ID="RegItemList" CONTENT="Trading Intermediary Registration Item List"/>
	<String ID="SoldItemList" CONTENT="Transaction agency waiting for sales settlement items"/>
	<String ID="BoughtItemList" CONTENT="Items awaiting settlement purchased by the trading intermediary"/>
	<String ID="RegCost" CONTENT="Registration Amount"/>
	<String ID="RegTime" CONTENT="Registration Time"/>
	<String ID="SoldCost" CONTENT="Settlement Amount"/>
	<String ID="SoldTime" CONTENT="Trading Time"/>
	<String ID="TradeBroker_TradeId" CONTENT="Label"/>
	<String ID="RegFee" CONTENT="Registration Fee"/>
	<String ID="TransFee" CONTENT="Transaction Fee"/>
	<String ID="TradeBroker_Seller" CONTENT="Seller"/>
	<String ID="TradeBroker_Buyer" CONTENT="Buyer"/>
	<String ID="BuyInstant" CONTENT="Buy Instant"/>
	<String ID="BuyDeal" CONTENT="Bargain"/>
	<String ID="ViewType" CONTENT="View Type"/>
	<String ID="ViewAll" CONTENT="Overall View"/>
	<String ID="ViewPlayingOnly" CONTENT="Only view the ongoing battlefield"/>
	<String ID="BattleFieldId" CONTENT="BattleFieldDBID"/>
	<String ID="BattleFieldTemplateId" CONTENT="BattleFieldTemplateId"/>
	<String ID="CreationTime" CONTENT="Creation Time"/>
	<String ID="FinishTime" CONTENT="End Time"/>
	<String ID="BlueTeam" CONTENT="BlueTeam"/>
	<String ID="RedTeam" CONTENT="Red Team"/>
	<String ID="TeamType" CONTENT="Team Category"/>
	<String ID="TeamPlanetId" CONTENT="Server ID"/>
	<String ID="TeamId" CONTENT="Team ID"/>
	<String ID="TeamDesc" CONTENT="Team Description"/>
	<String ID="TeamMemberCount" CONTENT="Number of members"/>
	<String ID="TeamScore" CONTENT="Score"/>
	<String ID="TreasureScore" CONTENT="Treasure Score"/>
	<String ID="TeamDeathCount" CONTENT="Deadwater"/>
	<String ID="BattleFieldPvP" CONTENT="Battlefield of Fighting Spirit"/>
	<String ID="BattleFieldStronghold" CONTENT="BattleFieldStronghold"/>
	<String ID="BattleFieldCannon" CONTENT="BattleFieldCannon"/>
	<String ID="BattleFieldKumas" CONTENT="Kumas World Battlefield"/>
	<String ID="BattleFieldSnowEvent" CONTENT="BattleFieldSnowEvent"/>
	<String ID="BattleFieldPvE" CONTENT="Reverse Field"/>
	<String ID="BattleHistory" CONTENT="Former Enemy"/>
	<String ID="ChangeBattleRecord" CONTENT="Comprehensive Modification"/>
	<String ID="ConfirmChangeBattleFieldRecord" CONTENT="Please check if the changed record is correct"/>
	<String ID="ChangeBattleInfo" CONTENT="Battle Information Correction"/>
	<String ID="ConfirmChangeBattleInfo" CONTENT="Please check that the battle information to be changed is correct"/>
	<String ID="BattleInformation" CONTENT="Battle Information"/>
	<String ID="StronHoldInformation" CONTENT="Spot Information"/>
	<String ID="DestroyInformation" CONTENT="Destroy Information"/>
	<String ID="ChangeExtraInfo" CONTENT="Modify Supplementary Information"/>
	<String ID="ConfirmChangeExtraInfo" CONTENT="Please check that the additional information to be changed is correct"/>
	<String ID="ChangeGradeScore" CONTENT="Change Personal Score"/>
	<String ID="ConfirmChangeGradeScore" CONTENT="Please check that the grade information to be changed is correct"/>
	<String ID="GradeScoreInformation" CONTENT="GradeScoreInformation"/>
	<String ID="Ranking" CONTENT="Battlefield Ranking"/>
	<String ID="LastWeekRanking" CONTENT="LastWeekRanking"/>
	<String ID="PlayCount" CONTENT="PlayCount"/>
	<String ID="Win" CONTENT="Multiply"/>
	<String ID="Draw" CONTENT="None"/>
	<String ID="Lose" CONTENT="Card"/>
	<String ID="Kill" CONTENT="Kill"/>
	<String ID="Death" CONTENT="Des"/>
	<String ID="Assist" CONTENT="Assist"/>
	<String ID="StageType" CONTENT="Stage Differentiation"/>
	<String ID="FirstHalf" CONTENT="Overall"/>
	<String ID="SecondHalf" CONTENT="Second Half"/>
	<String ID="GuardTowerRemainHp" CONTENT="Guard Tower Remaining HP"/>
	<String ID="RoundBonusTime" CONTENT="Get Time"/>
	<String ID="AttackStrongHold" CONTENT="Strong Attack"/>
	<String ID="DefenceStrongHold" CONTENT="DefenceStrongHold"/>
	<String ID="ObjectDestruction" CONTENT="Destruction of individuals on the battlefield"/>
	<String ID="DestroyCount" CONTENT="Destroy count"/>
	<String ID="RankingFormat" CONTENT="Last Week Ranking"/>
	<String ID="BattleScore" CONTENT="Battle Score"/>
	<String ID="StronHoldScore" CONTENT="Hold Score"/>
	<String ID="TotalScore" CONTENT="Total Score"/>
	<String ID="Rank" CONTENT="Rank"/>
	<String ID="CurrentRank" CONTENT="Latest Rank"/>
	<String ID="BestRank" CONTENT="Historical Rank"/>
	<String ID="SearchRanking" CONTENT="Battlefield Ranking Search"/>
	<String ID="WinRate" CONTENT="Win Rate"/>
	<String ID="NewCommander" CONTENT="New Commander"/>
	<String ID="GenerationSelect" CONTENT="Period Select"/>
	<String ID="GuardSelect" CONTENT="Guard Select"/>
	<String ID="SystemOnOff" CONTENT="Turn the political system on and off"/>
	<String ID="StateInfo" CONTENT="Council Information"/>
	<String ID="ReignInfo" CONTENT="Permanent Information"/>
	<String ID="CompetitionInfo" CONTENT="Competition Intelligence"/>
	<String ID="PointInfo" CONTENT="Point Info"/>
	<String ID="Enable" CONTENT="Enable"/>
	<String ID="Disable" CONTENT="Disable"/>
	<String ID="Enabled" CONTENT="Enabled"/>
	<String ID="Disabled" CONTENT="Disabled"/>
	<String ID="Show" CONTENT="Show"/>
	<String ID="Politics_Rank" CONTENT="Rank"/>
	<String ID="Point" CONTENT="Point"/>
	<String ID="GainVoteCount" CONTENT="Gain Vote"/>
	<String ID="Generation" CONTENT="Political Convergence"/>
	<String ID="SystemState" CONTENT="Online Status"/>
	<String ID="CurrentStep" CONTENT="Current Step"/>
	<String ID="EndTime" CONTENT="End Time"/>
	<String ID="NextStep" CONTENT="Next Step"/>
	<String ID="StartTime" CONTENT="Start Time"/>
	<String ID="LordInfo" CONTENT="Permanent Information"/>
	<String ID="LordName" CONTENT="LordName"/>
	<String ID="LordGuild" CONTENT="Affiliated Guild"/>
	<String ID="CompeteType" CONTENT="Compete Type"/>
	<String ID="GuardHuntingZone" CONTENT="Belonging to the hunting zone"/>
	<String ID="GuardTown" CONTENT="GuardTown"/>
	<String ID="LordPeriod" CONTENT="LordPeriod"/>
	<String ID="GuardNotice" CONTENT="GuardNotice"/>
	<String ID="GuardName" CONTENT="GuardName"/>
	<String ID="GuardId" CONTENT="GuardID"/>
	<String ID="TotalEarnedTax" CONTENT="Tax accumulated under current protection"/>
	<String ID="TaxForLord" CONTENT="TaxForLord"/>
	<String ID="LordTaxProfitRecord" CONTENT="Total Cumulative Profit"/>
	<String ID="PolicyPoint" CONTENT="Retention Policy Points"/>
	<String ID="TaxRateList" CONTENT="Tax Rate Status"/>
	<String ID="PolicyList" CONTENT="Policy Status"/>
	<String ID="RepresentList" CONTENT="Representation of capabilities"/>
	<String ID="ConsulCandidateList" CONTENT="Consul Candidate Status"/>
	<String ID="Policy" CONTENT="Policy"/>
	<String ID="PolicyId" CONTENT="Policy ID"/>
	<String ID="PolicyItemId" CONTENT="Policy ID"/>
	<String ID="TaxName" CONTENT="Tax Name"/>
	<String ID="TaxRate" CONTENT="Tax Rate"/>
	<String ID="MinTaxRate" CONTENT="MinTaxRate"/>
	<String ID="MaxTaxRate" CONTENT="Maximum Tax Rate"/>
	<String ID="NewTax" CONTENT="Tax to change"/>
	<String ID="NewTaxRob" CONTENT="Tax robbery to be changed"/>
	<String ID="VoteCandidateList" CONTENT="Vote Contest"/>
	<String ID="BattleFieldCandidateList" CONTENT="Battlefield Competition"/>
	<String ID="GuildWarCandidateList" CONTENT="Pre-Guild Competition"/>
	<String ID="VoteCandidateDbId" CONTENT="Candidate DBID"/>
	<String ID="ExpectedGuard" CONTENT="Expected Protection"/>
	<String ID="Pledge" CONTENT="Pledge"/>
	<String ID="LordGuildWarId" CONTENT="Lord Guild Pre-Election Guild ID"/>
	<String ID="LordGuildWarOpponentInfo" CONTENT="Relative Guild Information"/>
	<String ID="LordGuildWarGainPoint" CONTENT="Gain Points"/>
	<String ID="LordGuildWarPointLimit" CONTENT="Points from relative guild (1 day)"/>
	<String ID="ChangePledge" CONTENT="Change Pledge"/>
	<String ID="CompetitionPoint" CONTENT="Competition Point"/>
	<String ID="Dismiss" CONTENT="Dismiss"/>
	<String ID="LES_NORMAL" CONTENT="Governance Level"/>
	<String ID="LES_CANDIDACY" CONTENT="Candidate Stage"/>
	<String ID="LES_COMPETITION" CONTENT="Candidate Contention Phase"/>
	<String ID="LCT_BALLOT" CONTENT="Voting Contest"/>
	<String ID="LCT_BATTLE_FIELD" CONTENT="Battlefield Scoring Competition"/>
	<String ID="LCT_GUILD_WAR" CONTENT="Pre-Guild Competition"/>
	<String ID="ConfirmDisablePolitic" CONTENT="Please check the time"/>
	<String ID="ConfirmEnablePolitic" CONTENT="Please check the time"/>
	<String ID="ConfirmChangeGuardNotice" CONTENT="Please be aware of the guard notification that will be changed"/>
	<String ID="ConfirmChangeGuardPolicyPoint" CONTENT="Determine the guard policy point to change"/>
	<String ID="ConfirmChangeGuardPolicy" CONTENT="Please check the protection policy to change"/>
	<String ID="ConfirmChangeGuardTaxRate" CONTENT="Please confirm the guard rate to be changed"/>
	<String ID="ConfirmChangeLordCompetitionPoint" CONTENT="View upcoming changes to the permanent resident candidate competition score"/>
	<String ID="ConfirmChangeLordCompetitionPledge" CONTENT="Please confirm the permanent resident candidate commitment to be changed"/>
	<String ID="ConfirmDismissLord" CONTENT="Determine which guards will dismiss the lord"/>
	<String ID="ConfirmChangeGuardTax" CONTENT="Please check pending tax changes"/>
	<String ID="ConfirmResetVote" CONTENT="Please confirm the user and continent number to delete voting records"/>
	<String ID="ChangeProf" CONTENT="Change Proficiency"/>
	<String ID="ChangePetProf" CONTENT="Change Pet Proficiency"/>
	<String ID="ChangeHerbProf" CONTENT="Change Plant Proficiency"/>
	<String ID="ChangeEnergyProf" CONTENT="Change Periodic Proficiency"/>
	<String ID="ChangeBugProf" CONTENT="Insect Proficiency Change"/>
	<String ID="ChangeMineralProf" CONTENT="Change Mineral Proficiency"/>
	<String ID="PetProf" CONTENT="Pet Proficiency"/>
	<String ID="HerbProf" CONTENT="Plant Proficiency"/>
	<String ID="EnergyProf" CONTENT="Regular Proficiency"/>
	<String ID="BugProf" CONTENT="Insect Proficiency"/>
	<String ID="MineralProf" CONTENT="Mineral Proficiency"/>
	<String ID="ProfToChange" CONTENT="Replace Proficiency"/>
	<String ID="ConfirmChangeProf" CONTENT="Please check proficiency"/>
	<String ID="Pet" CONTENT="Pet"/>
	<String ID="Herb" CONTENT="Herb"/>
	<String ID="Energy" CONTENT="Energy"/>
	<String ID="Bug" CONTENT="Bug"/>
	<String ID="Mineral" CONTENT="Mineral"/>
	<String ID="CrestId" CONTENT="CrestId"/>
	<String ID="CrestName" CONTENT="CrestName"/>
	<String ID="EffectivePassiveName" CONTENT="Apply PassiveName"/>
	<String ID="Grade" CONTENT="Grade"/>
	<String ID="CrestParentId" CONTENT="Parent ID"/>
	<String ID="CostPoint" CONTENT="CostPoint"/>
	<String ID="CrestEffect" CONTENT="Crest Effect"/>
	<String ID="UsedCrest" CONTENT="UsedCrest"/>
	<String ID="AddCrest" CONTENT="Add Crest"/>
	<String ID="Crest_DelCrest" CONTENT="Delete Crest"/>
	<String ID="CrestPoint" CONTENT="Crest Point"/>
	<String ID="ChangeCrestPoint" CONTENT="Change Crest Point"/>
	<String ID="EquippedCrestInfo" CONTENT="Installation Crest Info"/>
	<String ID="UsedPoint" CONTENT="UsedPoint"/>
	<String ID="TotalPoint" CONTENT="Total Points"/>
	<String ID="CrestToAdd" CONTENT="Sentence to add"/>
	<String ID="ConfirmAddCrest" CONTENT="Please add crest"/>
	<String ID="ConfirmChangeCrestPoint" CONTENT="Please check the role sentence point"/>
	<String ID="Extraction" CONTENT="Extraction"/>
	<String ID="Craft" CONTENT="Crafting recipe"/>
	<String ID="RecipeToDelete" CONTENT="Recipe to delete"/>
	<String ID="RecipeToAdd" CONTENT="Recipe to add"/>
	<String ID="DeleteExtractRecipe" CONTENT="Delete Extract Recipe"/>
	<String ID="DeleteRecipe" CONTENT="Delete Recipe"/>
	<String ID="AddExtractRecipe" CONTENT="Add Extraction Recipe"/>
	<String ID="ConfirmDeleteRecipe" CONTENT="Please check the recipe to delete"/>
	<String ID="Produce" CONTENT="Complete"/>
	<String ID="Material" CONTENT="Processing"/>
	<String ID="Artisan_ChangeProf" CONTENT="Change crafting proficiency"/>
	<String ID="ResultItem" CONTENT="Result Item"/>
	<String ID="ChangeProduceSkill" CONTENT="Enter the skill value you want to change"/>
	<String ID="ChangeProduceSkillProf" CONTENT="How much does it cost"/>
	<String ID="ConfirmChangeProduceSkill" CONTENT="Please check the production skill value"/>
	<String ID="AddProduceRecipe" CONTENT="Add Production Recipe"/>
	<String ID="ConfirmAddProduceRecipe" CONTENT="Please view the recipe"/>
	<String ID="AddExtractSkill" CONTENT="Add Extraction Skill"/>
	<String ID="ConfirmAddExtractSkill" CONTENT="Please check the extraction ability to be added"/>
	<String ID="ExtractSkillToAdd" CONTENT="The extraction skill to add"/>
	<String ID="SetBookmark" CONTENT="Add Bookmark"/>
	<String ID="UnsetBookmark" CONTENT="Remove Bookmark"/>
	<String ID="TargetRecipe" CONTENT="Target Recipe"/>
	<String ID="ConfirmToggleBookmark" CONTENT="Please check the target label to change bookmark status"/>
	<String ID="NeedProf" CONTENT="Required Proficiency"/>
	<String ID="MaterialItem" CONTENT="Item Required"/>
	<String ID="ExtractionRank" CONTENT="Extraction Rank"/>
	<String ID="WhenWeaponExtract" CONTENT="Weapon Extractor"/>
	<String ID="WhenArmorExtract" CONTENT="Extract Defense Ball"/>
	<String ID="ChatType" CONTENT="Chat Type"/>
	<String ID="SearchDate" CONTENT="Search time"/>
	<String ID="DateStart" CONTENT="Start date and time"/>
	<String ID="DateEnd" CONTENT="End date and time"/>
	<String ID="Warn1" CONTENT="Member No is invalid"/>
	<String ID="Warn2" CONTENT="Incorrect time format or invalid range"/>
	<String ID="Warn3" CONTENT="The search period is more than 30 days old or does not exist. Yes) February 30"/>
	<String ID="Warn4" CONTENT="Invalid role name."/>
	<String ID="Warn5" CONTENT="DB UID is invalid"/>
	<String ID="LogId" CONTENT="LogId"/>
	<String ID="LogName" CONTENT="Log Name"/>
	<String ID="LogDesc" CONTENT="Log Description"/>
	<String ID="Log_DetailInfo" CONTENT="Details"/>
	<String ID="BattleTime" CONTENT="Battle Time"/>
	<String ID="Log_PartyTime" CONTENT="Party Time"/>
	<String ID="PartyCout" CONTENT="PartyCout"/>
	<String ID="GetCount" CONTENT="Get count"/>
	<String ID="TotalGetCount" CONTENT="Total Get Count"/>
	<String ID="KillerLevel" CONTENT="Killer Level"/>
	<String ID="KillerName" CONTENT="Killer Name"/>
	<String ID="KillPCName" CONTENT="Kill PC Name"/>
	<String ID="KillPCLevel" CONTENT="Kill PC Level"/>
	<String ID="Log_LogIn" CONTENT="Login"/>
	<String ID="Log_LogOut" CONTENT="Logout"/>
	<String ID="LevUp" CONTENT="Upgrade"/>
	<String ID="UserDead" CONTENT="User Dead"/>
	<String ID="UserKill" CONTENT="Kill"/>
	<String ID="GetExp" CONTENT="Get Exp"/>
	<String ID="LoggingIn" CONTENT="Logging in"/>
	<String ID="NoSelect" CONTENT="Not Selected"/>
	<String ID="FreeDist" CONTENT="Free Dist"/>
	<String ID="SequentialDist" CONTENT="Sequential"/>
	<String ID="HighGradeItem" CONTENT="Advanced"/>
	<String ID="RareGradeItem" CONTENT="Rare"/>
	<String ID="LegendaryGradeItem" CONTENT="Legend"/>
	<String ID="RandomMethod" CONTENT="Random Assign"/>
	<String ID="DiceMethod" CONTENT="Roll the dice"/>
	<String ID="EquipmentItem" CONTENT="Equipment Item"/>
	<String ID="PossibleClass" CONTENT="Possible Class"/>
	<String ID="BindOnLoot" CONTENT="Stroke"/>
	<String ID="ForbidLootOnBattle" CONTENT="Forbid LootOnBattle"/>
	<String ID="LT_USER_LOBBY_SELECT_CHAR" CONTENT="Start game in lobby"/>
	<String ID="LT_USER_ARBITER_LOGOUT_CHAR" CONTENT="Exit Game"/>
	<String ID="LT_USER_DEAD_BY_PC" CONTENT="Killed by PC"/>
	<String ID="LT_USER_DEAD_BY_NPC" CONTENT="Killed by NPC"/>
	<String ID="LT_USER_KILL_PC" CONTENT="PC Hunt"/>
	<String ID="LT_USER_KILL_NPC" CONTENT="NPC hunting"/>
	<String ID="LT_USER_EARN_EXP" CONTENT="Get EXP"/>
	<String ID="LT_USER_EQUIPMENTS_1_R" CONTENT="Equipment Status 1"/>
	<String ID="LT_USER_EQUIPMENTS_2_R" CONTENT="Equipment Status 2"/>
	<String ID="LT_USER_WEAPON_CUSTOMS_R" CONTENT="Custom Status 1"/>
	<String ID="LT_USER_BODY_CUSTOMS_R" CONTENT="Custom Status 2"/>
	<String ID="LT_USER_RESURRECT" CONTENT="Resurrection"/>
	<String ID="LT_USER_FALLING_DAMAGE" CONTENT="Fall Damage"/>
	<String ID="LT_USER_USER_DAMAGE" CONTENT="PC takes damage"/>
	<String ID="LT_USER_ASK_RESURRECT" CONTENT="Ask Resurrection"/>
	<String ID="LT_CREATE_PARTY" CONTENT="Create Party"/>
	<String ID="LT_JOIN_PARTY" CONTENT="Join the party"/>
	<String ID="LT_DISMISS_PARTY" CONTENT="Dismiss Party"/>
	<String ID="LT_LEAVE_PARTY" CONTENT="Leaving Party"/>
	<String ID="LT_KICKED_PARTY" CONTENT="Expired from the party"/>
	<String ID="LT_KICK_PARTY" CONTENT="Expel Party"/>
	<String ID="LT_CHANGE_MANAGER_PARTY" CONTENT="Replace party venue"/>
	<String ID="LT_CHANGE_LOOTINGMETHOD_PARTY" CONTENT="Setup Project Routine"/>
	<String ID="LT_USER_PARTY_BOARD_WRITE" CONTENT="Recruit party participation"/>
	<String ID="LT_USER_PARTY_BOARD_APPLY" CONTENT="Attempting to recruit a party"/>
	<String ID="LT_GUILD_CREATE" CONTENT="Create Guild"/>
	<String ID="LT_GUILD_APPLY" CONTENT="Subscribe to Guild"/>
	<String ID="LT_GUILD_ACCEPT" CONTENT="Accept Guild Subscription"/>
	<String ID="LT_GUILD_REJECT" CONTENT="Refused to join the guild"/>
	<String ID="LT_GUILD_JOIN" CONTENT="Join a guild"/>
	<String ID="LT_GUILD_DISMISS" CONTENT="Disband the guild"/>
	<String ID="LT_GUILD_LEAVE" CONTENT="Leave Guild"/>
	<String ID="LT_GUILD_BAN" CONTENT="Guild Class"/>
	<String ID="LT_GUILD_CHANGE_MANAGER" CONTENT="Replace Guild Chapter"/>
	<String ID="LT_GUILD_CHANGE_LEVEL" CONTENT="Replace Guild Level"/>
	<String ID="LT_USER_PROF_MINERAL" CONTENT="Proficiency Minerals"/>
	<String ID="LT_USER_PROF_BUG" CONTENT="Proficiency Insect"/>
	<String ID="LT_USER_PROF_ENERGY" CONTENT="Proficiency Periodic"/>
	<String ID="LT_USER_PROF_HERB" CONTENT="Proficiency Plant"/>
	<String ID="LT_USER_PROF_PET" CONTENT="proficiency pet"/>
	<String ID="LT_USER_SKILL_PROF" CONTENT="Manufacturing Proficiency"/>
	<String ID="LT_USER_COLLECTION_TRY" CONTENT="Try to collect"/>
	<String ID="LT_USER_COLLECTION_SUCCESS" CONTENT="Collect successful"/>
	<String ID="LT_USER_COLLECTION_FAIL" CONTENT="Collection failed"/>
	<String ID="LT_USER_COLLECTION_CANCEL" CONTENT="Cancel collection"/>
	<String ID="LT_CAMP_TELEPORT" CONTENT="Campaign Mobile"/>
	<String ID="LT_TELEPORT" CONTENT="Move"/>
	<String ID="LT_BEGIN_PEGASUS" CONTENT="Welcome"/>
	<String ID="LT_END_PEGASUS" CONTENT="Pegasus Ends"/>
	<String ID="LT_ADD_FRIEND" CONTENT="Add friend"/>
	<String ID="LT_DEL_FRIEND" CONTENT="Delete friend"/>
	<String ID="LT_USER_CHANGE_CHANNEL" CONTENT="Mobile Channel"/>
	<String ID="LT_TELEPORT_ACCEPT" CONTENT="Accept Move"/>
	<String ID="LT_TELEPORT_REJECT" CONTENT="Reject move"/>
	<String ID="LT_NEARTOWN_ESCAPE" CONTENT="Emergency Escape"/>
	<String ID="LT_HOMUN_INCUBATOR_START" CONTENT="Humen Incubation Start"/>
	<String ID="LT_HOMUN_INCUBATOR_COMPLETE" CONTENT="Humen hatching completed"/>
	<String ID="LT_HOMUN_INCUBATOR_SUCCESS" CONTENT="Humen hatched successfully"/>
	<String ID="LT_HOMUN_INCUBATOR_FAIL" CONTENT="Humen hatching failed"/>
	<String ID="LT_HOMUN_CALL_PET" CONTENT="Jiaohao"/>
	<String ID="LT_HOMUN_REPAIR_PET" CONTENT="Repair Pet"/>
	<String ID="LT_HOMUN_DISMISS_PET" CONTENT="Withdraw Pet"/>
	<String ID="LT_HOMUN_DEAD_PET" CONTENT="pet dead"/>
	<String ID="LT_HOMUN_EQUIP_ORB" CONTENT="Load Ball"/>
	<String ID="LT_HOMUN_UNEQUIP_ORB" CONTENT="Teardown"/>
	<String ID="LT_HOMUN_EQUIP_FAIL_ORB" CONTENT="Installation failed"/>
	<String ID="LT_CREST_ADD" CONTENT="Registration Statement"/>
	<String ID="LT_CREST_DEL" CONTENT="Delete Statement"/>
	<String ID="LT_CREST_APPLY" CONTENT="Apply Statement"/>
	<String ID="LT_CREST_DIS_APPLY" CONTENT="Cancel Statement"/>
	<String ID="LT_VEHICLE_MOUNT" CONTENT="Ride"/>
	<String ID="LT_VEHICLE_UNMOUNT" CONTENT="Get off"/>
	<String ID="LT_CHANGE_USER_RACE" CONTENT="Race Change Using Additional Services"/>
	<String ID="LT_CHANGE_USER_GENDER" CONTENT="Use Additional Service Gender Change"/>
	<String ID="LT_CHANGE_USER_LOOKS" CONTENT="Appearance changes with additional services"/>
	<String ID="UserSkillUse" CONTENT="Use Skill"/>
	<String ID="UserSkillResult" CONTENT="Skill Result"/>
	<String ID="UserSkillLearn" CONTENT="Learn Skill"/>
	<String ID="LT_USER_SKILL_CANCEL_BY_PC" CONTENT="Cancel Skill (PC)"/>
	<String ID="LT_USER_SKILL_CANCEL_BY_NPC" CONTENT="Cancel Skill (NPC)"/>
	<String ID="LT_USER_SKILL_LEARN" CONTENT="Learn Skill"/>
	<String ID="LT_USER_SKILL_FORGET" CONTENT="Forget Skill"/>
	<String ID="LT_USER_RECIPE_LEARN" CONTENT="Learn Recipe"/>
	<String ID="LT_USER_RECIPE_FORGET" CONTENT="Discard recipe"/>
	<String ID="UserItemIn" CONTENT="Get Item"/>
	<String ID="UserItemOut" CONTENT="Item is missing"/>
	<String ID="LT_USER_ITEM_USE" CONTENT="Use Item"/>
	<String ID="LT_USER_ITEM_ABANDON" CONTENT="Drop Item"/>
	<String ID="LT_USER_STORE_ITEM_BUY" CONTENT="Buy from Store"/>
	<String ID="LT_USER_STORE_ITEM_SELL" CONTENT="Sell as Store"/>
	<String ID="LT_USER_WAREHOUSE_ITEM_PUT" CONTENT="Put in subinventory (style subinventory)"/>
	<String ID="LT_USER_WAREHOUSE_ITEM_GET" CONTENT="Find in Subinventory (Style Subinventory)"/>
	<String ID="LT_USER_WAREHOUSE_PAY_COMMISSION" CONTENT="Warehouse Fee"/>
	<String ID="LT_USER_TRADE_ITEM_IN" CONTENT="Get Trade"/>
	<String ID="LT_USER_TRADE_ITEM_OUT" CONTENT="Export Exchange"/>
	<String ID="LT_USER_TRADE_BEGIN" CONTENT="Start trading"/>
	<String ID="LT_USER_TRADE_END" CONTENT="Trading completed"/>
	<String ID="LT_USER_TRADE_POSTUP_ITEM" CONTENT="Put prop in transaction window"/>
	<String ID="LT_USER_TRADE_REMOVE_ITEM" CONTENT="Remove Item from Transaction Window"/>
	<String ID="LT_USER_TRADE_SET_MONEY" CONTENT="Enter cash in transaction window"/>
	<String ID="LT_USER_TRADE_ASK_TRADE" CONTENT="Click the Request Transaction button"/>
	<String ID="LT_USER_TRADE_ACCEPT_TRADE" CONTENT="Final Accepted Transaction"/>
	<String ID="LT_ITEM_ENCHANT_TARGET" CONTENT="Set the target item to be enhanced."/>
	<String ID="LT_ITEM_ENCHANT_UNSET_TARGET" CONTENT="Release the target item to be enhanced."/>
	<String ID="LT_ITEM_ENCHANT_MATERIAL" CONTENT="Sets the enhanced master material item."/>
	<String ID="LT_ITEM_ENCHANT_UNSET_MATERIAL" CONTENT="Release Enhanced Master Material Item."/>
	<String ID="LT_ITEM_ENCHANT_SCROLL" CONTENT="Set scroll items to enhance."/>
	<String ID="LT_ITEM_ENCHANT_UNSET_SCROLL" CONTENT="Turn off enhanced scrolling items."/>
	<String ID="LT_ITEM_ENCHANT_SUCCESS" CONTENT="Enhanced success, enchant is +."/>
	<String ID="LT_ITEM_ENCHANT_FAIIL" CONTENT="Enhancement failed, enchant becomes -"/>
	<String ID="LT_ITEM_ENCHANT_EMPTY" CONTENT="Enchantment failed, but invalidated it, leaving the enchant unchanged."/>
	<String ID="LT_ITEM_ENCHANT_DELETED_ITEM" CONTENT="Items that disappear during enhancement"/>
	<String ID="LT_VENDING_START_SELLING" CONTENT="Start the vending machine vending machine."/>
	<String ID="LT_VENDING_START_BUYING" CONTENT="Start vending machine purchase."/>
	<String ID="LT_VENDING_STOP" CONTENT="Start selling vending machines."/>
	<String ID="LT_VENDING_CLOSE" CONTENT="Close Vending Machine"/>
	<String ID="LT_VENDING_SELL_ITEM" CONTENT="Item information sold by the vending machine."/>
	<String ID="LT_VENDING_BUY_ITEM" CONTENT="About items purchased at vending machines."/>
	<String ID="LT_VENDING_SETTING_DEL_ITEM" CONTENT="Place items into the vending machine when setting up the vending machine where the item will be sold."/>
	<String ID="LT_VENDING_SETTING_RECV_ITEM" CONTENT="Get an item from the vending machine when setting up the vending machine that sells the item."/>
	<String ID="LT_VENDING_SETTING_DEPOSITDELTA" CONTENT="Single Item Company provides deposit delta to vending machine during vending machine setup."/>
	<String ID="LT_VENDING_ITEM_BUY" CONTENT="Buy items at the vending machine."/>
	<String ID="LT_VENDING_ITEM_BUYPRICE" CONTENT="Total money paid in item fraud at vending machines."/>
	<String ID="LT_VENDING_ITEM_SELL" CONTENT="Sell the item to a vending machine."/>
	<String ID="LT_VENDING_ITEM_SELLPRICE" CONTENT="Total price of item sold to vending machine."/>
	<String ID="LT_VENDING_CLEAR_RECV_ITEM" CONTENT="Item received at checkout from the vending machine."/>
	<String ID="LT_VENDING_CLEAR_RECV_MONEY" CONTENT="Money received from the vending machine at checkout."/>
	<String ID="LT_VENDING_OWNER_ITEM_BUY" CONTENT="Item information purchased by vending machine"/>
	<String ID="LT_VENDING_OWNER_ITEM_SELL" CONTENT="Item information sold by vending machine"/>
	<String ID="LT_USER_GAMBLE_SUCCESS" CONTENT="Pandora success"/>
	<String ID="LT_USER_GAMBLE_SUCCESS_SPECIAL" CONTENT="Pandora Box Special Success"/>
	<String ID="LT_USER_GAMBLE_FAIL" CONTENT="Pandora Box failed"/>
	<String ID="LT_USER_GAMBLE_CANCEL" CONTENT="Cancel Pandora Box"/>
	<String ID="LT_ITEM_EQUIP" CONTENT="Equipment Item"/>
	<String ID="LT_ITEM_UNEQUIP" CONTENT="Uninstall Item"/>
	<String ID="LT_ITEM_ATTACH_CUSTOMIZE" CONTENT="Install the Enhancement Crystal."/>
	<String ID="LT_ITEM_DETTACH_CUSTOMIZE" CONTENT="Minus Enhancement Crystals."/>
	<String ID="LT_DEAD_CUSTOMIZE_DESTROY" CONTENT="When the crystal shatters after death."/>
	<String ID="LT_ITEM_PRODUCE_SUCCESS" CONTENT="Project created after the finished project is successfully produced."/>
	<String ID="LT_ITEM_PRODUCE_FAIL" CONTENT="Attempt to build project failed"/>
	<String ID="LT_ITEM_PRODUCE_DELETED" CONTENT="Items lost while making finished items"/>
	<String ID="LT_ITEM_EXTRACT_SUCCESS" CONTENT="Items successfully extracted for items"/>
	<String ID="LT_ITEM_EXTRACT_FAIL" CONTENT="Item extraction failed"/>
	<String ID="LT_ITEM_EXTRACT_RESULT" CONTENT="Item Extraction Results"/>
	<String ID="LT_ITEM_RECIPE_BOOKMARK" CONTENT="Unregister Item Recipe Favorites"/>
	<String ID="LT_USER_GROUP_DUEL_START" CONTENT="Start group duel"/>
	<String ID="LT_USER_GROUP_DUEL_END" CONTENT="End Group Duel"/>
	<String ID="LT_USER_DECLARE_PK" CONTENT="PK declaration"/>
	<String ID="LT_USER_INCREASE_PKPOINT" CONTENT="PK point increment"/>
	<String ID="LT_GROUP_DUEL_CREATE" CONTENT="Create Group Duel"/>
	<String ID="LT_GROUP_DUEL_CHANGE_SETTING" CONTENT="Group Duel Settings"/>
	<String ID="LT_GROUP_DUEL_CHANGE_SLOT" CONTENT="Alternative Group Duel Slot"/>
	<String ID="LT_GROUP_DUEL_BECOME_REPRESENT" CONTENT="Group Duel"/>
	<String ID="LT_GROUP_DUEL_JOIN" CONTENT="Join the Group Duel"/>
	<String ID="LT_GROUP_DUEL_LEAVE" CONTENT="Leave Duel"/>
	<String ID="LT_GROUP_DUEL_BETTING_ITEM" CONTENT="Group Duel Item Bets"/>
	<String ID="LT_GROUP_DUEL_BETTING_MONEY" CONTENT="Group Duel Money Bets"/>
	<String ID="LT_GROUP_DUEL_GET_POINT" CONTENT="Get Group Duel Points"/>
	<String ID="LT_GROUP_DUEL_RECV_ITEM" CONTENT="Get Group Duel Item"/>
	<String ID="LT_GROUP_DUEL_RECV_MONEY" CONTENT="Group duel for money"/>
	<String ID="LT_USER_CHAT" CONTENT="Chat"/>
	<String ID="LT_USER_WHISPER" CONTENT="Whisper"/>
	<String ID="QuestStart" CONTENT="Start Quest"/>
	<String ID="QuestEnd" CONTENT="End Quest"/>
	<String ID="PKStart" CONTENT="PKStart"/>
	<String ID="PKEnd" CONTENT="End PK"/>
	<String ID="PVPStart" CONTENT="PVP Start"/>
	<String ID="PVPEnd" CONTENT="PVP End"/>
	<String ID="Gamble" CONTENT="Gamble"/>
	<String ID="Chat" CONTENT="Chat"/>
	<String ID="Whisper" CONTENT="Whisper"/>
	<String ID="UserCreate" CONTENT="Create User"/>
	<String ID="UserDel" CONTENT="Delete User"/>
	<String ID="UserDelCancel" CONTENT="Cancel User Delete"/>
	<String ID="TextCmd" CONTENT="text command"/>
	<String ID="NPCDead" CONTENT="NPC Dead"/>
	<String ID="NPCItemDrop" CONTENT="NPC Item"/>
	<String ID="Log_PlayTime" CONTENT="Connection time (seconds)"/>
	<String ID="LevUpTime" CONTENT="Upgrade time (seconds)"/>
	<String ID="TryCount" CONTENT="Number of attempts"/>
	<String ID="isWin" CONTENT="Success or not"/>
	<String ID="GambleCount" CONTENT="try/get count"/>
	<String ID="IsActive" CONTENT="Enable"/>
	<String ID="Log_SearchType" CONTENT="Classification"/>
	<String ID="DBUID" CONTENT="DB UID"/>
	<String ID="ItemUID" CONTENT="ItemUID"/>
	<String ID="GuildUID" CONTENT="GuildUID"/>
	<String ID="NPCUID" CONTENT="NPC UID"/>
	<String ID="Log_GuardId" CONTENT="Guard ID (Politics Only)"/>
	<String ID="Log_GuardName" CONTENT="Guard Name"/>
	<String ID="ActionList" CONTENT="Action List"/>
	<String ID="User" CONTENT="Role"/>
	<String ID="NPC" CONTENT="NPC"/>
	<String ID="Item" CONTENT="Item"/>
	<String ID="ETC" CONTENT="Other"/>
	<String ID="Cheating" CONTENT="Cheating"/>
	<String ID="NPCName" CONTENT="NPC name"/>
	<String ID="PartyMember" CONTENT="PartyMember"/>
	<String ID="SelectAllUser" CONTENT="Select All Users"/>
	<String ID="SelectAllItem" CONTENT="Select All Items"/>
	<String ID="SelectAllEtc" CONTENT="Select All Other"/>
	<String ID="SelectAllCheating" CONTENT="Select AllCheating"/>
	<String ID="SelectAllAction" CONTENT="Select the entire action"/>
	<String ID="SelectAllGuildComp" CONTENT="Guild Competitive Select All"/>
	<String ID="SearchByCharName" CONTENT="Search Character"/>
	<String ID="SearchByEtc" CONTENT="Search Field"/>
	<String ID="SearchByLogAction" CONTENT="Search By Log Action"/>
	<String ID="LT_TS_CHANGE_MONEY" CONTENT="Money Change"/>
	<String ID="LT_TS_CHANGE_ITEM_OWNER_OUT" CONTENT="Export item"/>
	<String ID="LT_TS_CHANGE_ITEM_OWNER_IN" CONTENT="Get Item"/>
	<String ID="LT_TS_CHANGE_ITEM_AMOUNT" CONTENT="Change item quantity"/>
	<String ID="LT_TS_CHANGE_ITEM_POS" CONTENT="Change item location"/>
	<String ID="LT_TS_ENCHANT_ITEM" CONTENT="Enhancement Item"/>
	<String ID="LT_TS_CUSTOMIZING_ITEM" CONTENT="Item Crystal"/>
	<String ID="LT_TS_DELETE_ITEM" CONTENT="Delete item"/>
	<String ID="LT_TS_INSERT_STACKABLE_ITEM" CONTENT="Get quantitative item"/>
	<String ID="LT_TS_INSERT_NONSTACKABLE_ITEM" CONTENT="Get non-quantitative items"/>
	<String ID="LT_TS_WARE_CHANGE_MONEY" CONTENT="Money change in warehouse"/>
	<String ID="LT_TS_WARE_MOVE_ITEM" CONTENT="Archive item to warehouse"/>
	<String ID="LT_TS_WARE_GET_ITEM" CONTENT="Find item in repository"/>
	<String ID="LT_TS_WAREINVEN_DEL_MONEY" CONTENT="Delete money from warehouse"/>
	<String ID="LT_TS_INSERT_NONSTACKABLE_STOREITEM" CONTENT="Fraud non-quantitative items from store"/>
	<String ID="LT_TS_PARCEL_CHANGE_ITEM_AMOUNT" CONTENT="Change Parcel Item Amount"/>
	<String ID="LT_TS_PARCEL_MOVE_ITEM" CONTENT="Send non-quantity item parcel"/>
	<String ID="LT_TS_PARCEL_RECV_ITEM" CONTENT="Find Parcel Item"/>
	<String ID="LT_TS_VM_DEL_ITEM" CONTENT="Delete items registered on vending machine"/>
	<String ID="LT_TS_VM_ADD_STACKABLE_ITEM" CONTENT="Register quantity item on vending machine"/>
	<String ID="LT_TS_VM_ADD_NONSTACKABLE_ITEM" CONTENT="Register Non-Quantitative Items at Vending Machines"/>
	<String ID="LT_TS_SEND_GROUP_DUEL_NONSTACKABLE_ITEM" CONTENT="Send Group Duel Item"/>
	<String ID="LT_TS_RECV_GROUP_DUEL_ITEM" CONTENT="Find Group Duel Item"/>
	<String ID="LT_TS_TRADE_BROKER_PUT_ITEM" CONTENT="Send item to auction house"/>
	<String ID="LT_TS_TRADE_BROKER_GET_ITEM" CONTENT="Find items at auction"/>
	<String ID="LT_TS_HOMUN_EQUIP_ITEM" CONTENT="Register Pet"/>
	<String ID="LT_TS_HOMUN_UNEQUIP_ITEM" CONTENT="Untack"/>
	<String ID="LT_TS_SEAL_ITEM" CONTENT="Project Attribution"/>
	<String ID="LT_MAIL_NORMAL_SENT" CONTENT="Normal Mail Sending"/>
	<String ID="LT_MAIL_PRESENT_SENT" CONTENT="Send a gift"/>
	<String ID="LT_MAIL_PRESENT_SENT_ITEM" CONTENT="Additional Gift"/>
	<String ID="LT_MAIL_PRESENT_RECEIVED" CONTENT="Received gift email attachment"/>
	<String ID="LT_MAIL_ESCROW_SENT" CONTENT="Send to Password"/>
	<String ID="LT_MAIL_ESCROW_SENT_ITEM" CONTENT="Append to Password"/>
	<String ID="LT_MAIL_ESCROW_ACCEPTED" CONTENT="Accept Password"/>
	<String ID="LT_MAIL_ESCROW_RETURNED" CONTENT="Return to Password"/>
	<String ID="LT_TS_TRADE_BROKER_REG_ITEM" CONTENT="Register item at auction"/>
	<String ID="LT_TS_TRADE_BROKER_UNREG_ITEM" CONTENT="Delete items registered at auction"/>
	<String ID="LT_TS_TRADE_BROKER_UPDATE_ITEM" CONTENT="Modify auction room registration information"/>
	<String ID="LT_TS_TRADE_BROKER_CALC_SOLD_ITEM" CONTENT="Auction House Sales Item Actuarial"/>
	<String ID="LT_TS_TRADE_BROKER_CALC_BOUGHT_ITEM" CONTENT="Auction house purchase item actuarial"/>
	<String ID="LT_TS_WARE_CHANGE_ITEM_AMOUNT" CONTENT="Store quantitative items in the warehouse"/>
	<String ID="LT_BATTLE_FIELD_CREATE_TEAM" CONTENT="Create Battlefield Team"/>
	<String ID="LT_BATTLE_FIELD_CHANGE_COMMANDER" CONTENT="Change Battlefield Commander"/>
	<String ID="LT_BATTLE_FIELD_WITHDRAW" CONTENT="Battlefield Escape"/>
	<String ID="LT_BATTLE_FIELD_FIELD_REVIVE" CONTENT="Battlefield Resurrection"/>
	<String ID="LT_BATTLE_FIELD_INVITE_TEAM" CONTENT="Invite Battlefield Team"/>
	<String ID="LT_BATTLE_FIELD_REJECT_TEAM_INVITATION" CONTENT="Refused to invite battle teams"/>
	<String ID="LT_BATTLE_FIELD_MATCH_REGISTER" CONTENT="Battle Match Registration"/>
	<String ID="LT_BATTLE_FIELD_MATCH_SUCCESS" CONTENT="Battle match successfully"/>
	<String ID="LT_BATTLE_FIELD_CREATED" CONTENT="Create Battlefield Instance"/>
	<String ID="LT_BATTLE_FIELD_ENTER" CONTENT="Battlefield Position"/>
	<String ID="LT_BATTLE_FIELD_GIVE_UP" CONTENT="Give up the battlefield, give up on me"/>
	<String ID="LT_BATTLE_FIELD_WIN" CONTENT="Battlefield Victory"/>
	<String ID="LT_BATTLE_FIELD_LOSE" CONTENT="Battlefield Failure"/>
	<String ID="LT_BATTLE_FIELD_DRAW" CONTENT="Battlefield Draw"/>
	<String ID="LT_BATTLE_FIELD_REWARD" CONTENT="Pay Battlefield Compensation"/>
	<String ID="LT_CANNON_BATTLE_FIELD_WIN" CONTENT="Battlefield Victory by Artillery Fire"/>
	<String ID="LT_CANNON_BATTLE_FIELD_LOSE" CONTENT="Battlefield of artillery failure"/>
	<String ID="LT_CANNON_BATTLE_FIELD_DRAW" CONTENT="Battlefield draw by artillery fire"/>
	<String ID="LT_CANNON_BATTLE_FIELD_CRASH_NPC" CONTENT="Cannon Fire Destroys Battlefield NPC"/>
	<String ID="LT_CANNON_BATTLE_FIELD_END_OF_STAGE" CONTENT="End Saturated Battlefield Round"/>
	<String ID="LT_CANNON_BATTLE_FIELD_GET_BONUS_TIME" CONTENT="Get saturated battlefield round time"/>
	<String ID="LT_BATTLE_FIELD_USER_CHAT_BAN" CONTENT="Battle Chat Ban"/>
	<String ID="LT_UPDATE_BATTLE_FIELD_SEASON_RANKER" CONTENT="Battlefield Season Ranke Update"/>
	<String ID="LT_KUMAS_BATTLE_FIELD_WIN" CONTENT="KUMAS World Battlefield Victory"/>
	<String ID="LT_KUMAS_BATTLE_FIELD_LOSE" CONTENT="KUMAS World Battlefield Failed"/>
	<String ID="LT_KUMAS_BATTLE_FIELD_DRAW" CONTENT="KUMAS World Battlefield Draw"/>
	<String ID="KBF_KumasTeam" CONTENT="KumasTeam"/>
	<String ID="KBF_HumanTeam" CONTENT="Human Team"/>
	<String ID="LT_SNOW_EVENT_BATTLE_FIELD_WIN" CONTENT="Snowball battle victory"/>
	<String ID="LT_SNOW_EVENT_BATTLE_FIELD_LOSE" CONTENT="Snowball fight failed"/>
	<String ID="LT_SNOW_EVENT_BATTLE_FIELD_DRAW" CONTENT="Snowball Battlefield Draw"/>
	<String ID="LT_ROUND_PVE_BATTLE_FIELD_WIN" CONTENT="PVE Competitive Content Win"/>
	<String ID="LT_ROUND_PVE_BATTLE_FIELD_LOSE" CONTENT="PVE competition content failed"/>
	<String ID="LT_ROUND_PVE_BATTLE_FIELD_DRAW" CONTENT="PVE Competitive Content Draw"/>
	<String ID="LT_ROUND_PVE_BATTLE_FIELD_USE_SKILL_POINT" CONTENT="Use PVE to compete for content key points"/>
	<String ID="LT_COUPON_USE_SUCCESS" CONTENT="Use the box successfully"/>
	<String ID="LT_COUPON_DELETE_SUCCESS" CONTENT="Successfully delete box"/>
	<String ID="LT_COUPON_RECEIVE_ITEM" CONTENT="Receiver Box Item"/>
	<String ID="LT_COUPON_START_USE_FAILED" CONTENT="Cannot start using box"/>
	<String ID="LT_COUPON_COMMIT_USE_FAILED" CONTENT="Commit use box failed"/>
	<String ID="LT_COUPON_ROLLBACK_USE_FAILED" CONTENT="Use box rollback failed"/>
	<String ID="LT_COUPON_DELETE_FAILED" CONTENT="Failed to delete box"/>
	<String ID="LT_TRADE_BROKER_REGISTERED" CONTENT="Trade Brokered Goods Registration"/>
	<String ID="LT_TRADE_BROKER_UNREGISTERED" CONTENT="Unregistration of Trade Brokered Goods"/>
	<String ID="LT_TRADE_BROKER_EXPIRED" CONTENT="Trading broker registration expires"/>
	<String ID="LT_TRADE_BROKER_BOUGHT" CONTENT="Transaction Broker Purchases Goods"/>
	<String ID="LT_TRADE_BROKER_SOLD" CONTENT="Trade broker selling goods"/>
	<String ID="LT_TRADE_BROKER_CALC_BOUGHT" CONTENT="Transaction Broker Purchase Settlement"/>
	<String ID="LT_TRADE_BROKER_CALC_SOLD" CONTENT="Trade brokerage sales settlement"/>
	<String ID="LT_TRADE_BROKER_SUGGEST_DEAL" CONTENT="Trading broker offers a bargain"/>
	<String ID="LT_TRADE_BROKER_DEAL_START" CONTENT="Start trade broker bargain"/>
	<String ID="LT_TRADE_BROKER_DEAL_SUCCEEDED" CONTENT="The transaction broker successfully negotiated the price"/>
	<String ID="LT_TRADE_BROKER_DEAL_FAILED" CONTENT="Trading broker failed to bargain"/>
	<String ID="LT_OPERATOR_ADD_ITEM" CONTENT="Add Operation Tool Item"/>
	<String ID="LT_OPERATOR_DEL_ITEM" CONTENT="Delete Operation Tool Item"/>
	<String ID="LT_OPERATOR_DEL_WARE_ITEM" CONTENT="Delete Operational Tool Warehouse Item"/>
	<String ID="LT_OPERATOR_DEL_ITEMTEMPLATEID" CONTENT="Delete Operation Tool Warehouse Item Type"/>
	<String ID="LT_OPERATOR_CHANGE_MONEY" CONTENT="Change Operation Tool Value"/>
	<String ID="LT_OPERATOR_MAKE_ADMIN" CONTENT="Set Operation Tool Manager"/>
	<String ID="LT_OPERATOR_DELETE_QUEST" CONTENT="Delete Operation Tool Task"/>
	<String ID="LT_OPERATOR_ADD_QUEST" CONTENT="Add Operation Tool Task"/>
	<String ID="LT_OPERATOR_EDIT_QUEST" CONTENT="Modify Operation Tool Task"/>
	<String ID="LT_OPERATOR_ADD_SKILL" CONTENT="Modify Operation Tool Skill"/>
	<String ID="LT_OPERATOR_DEL_SKILL" CONTENT="Delete Operation Tool Skill"/>
	<String ID="LT_OPERATOR_CHANGE_WAREMONEY" CONTENT="Change Operational Tool Warehouse Value"/>
	<String ID="LT_OPERATOR_CHANGE_PROF" CONTENT="Change Operator Tool Proficiency"/>
	<String ID="LT_OPERATOR_CHANGE_CREST" CONTENT="Change Operation Tool Statement"/>
	<String ID="LT_OPERATOR_CHANGE_ACHIEVEMENT" CONTENT="Operation Tool Performance Change"/>
	<String ID="LT_OPERATOR_CHANGE_ARTISAN" CONTENT="Change Production Tool Crafting Proficiency"/>
	<String ID="LT_OPERATOR_ADD_FRIEND" CONTENT="Add Operation Tool Friend"/>
	<String ID="LT_OPERATOR_DEL_FRIEND" CONTENT="Delete OPERATOR Friend"/>
	<String ID="LT_OPERATOR_ADD_RESTRICTION" CONTENT="Add Operational Tool Sanctions"/>
	<String ID="LT_OPERATOR_DEL_RESTRICTION" CONTENT="Delete Operation Tool Sanctions"/>
	<String ID="LT_OPERATOR_UNDELETE_USER" CONTENT="Restore Operation Tool User"/>
	<String ID="LT_OPERATOR_RENAME_USER" CONTENT="Rename Production Tool User"/>
	<String ID="LT_OPERATOR_DEL_TRADE_BROKER_ITEM" CONTENT="Delete Action Tool Auction"/>
	<String ID="LT_OPERATOR_USER_TELEPORT" CONTENT="Operation Tool Transmission"/>
	<String ID="LT_OPERATOR_CHANGE_SERVER_SETTING" CONTENT="Set Production Tools Server"/>
	<String ID="LT_OPERATOR_CHANGE_USER_LEVEL" CONTENT="Production Tool User Level"/>
	<String ID="LT_OPERATOR_CHANGE_USER_EXP" CONTENT="Operation Tool User Experience"/>
	<String ID="LT_OPERATOR_STOP_VENDING_MACHINE" CONTENT="Stop Production Tool Vending Machine"/>
	<String ID="LT_OPERATOR_SEIZURE_VENDING_MACHINE" CONTENT="Operation Tool Seizure Vending Machine"/>
	<String ID="LT_OPERATOR_SEIZURE_MONEY" CONTENT="Operation Tool Gold Seizure"/>
	<String ID="LT_OPERATOR_RETURN_SEIZURE_MONEY" CONTENT="Operation Tool Seizure Gold Recovery"/>
	<String ID="LT_OPERATOR_SEIZURE_PARCEL" CONTENT="Operation Tool Post Seizure"/>
	<String ID="LT_OPERATOR_DELETE_SEIZURE_ITEM" CONTENT="Delete Operation Tool Seizure Item"/>
	<String ID="LT_OPERATOR_DELETE_SEIZURE_MONEY" CONTENT="Delete Operation Tool Seize Gold"/>
	<String ID="LT_OPERATOR_CHANGE_USER_PKPOINT" CONTENT="Change Operation Tool Role PK Point"/>
	<String ID="LT_OPERATOR_SET_RESTBONUS_POINT" CONTENT="Change Operation Tool Character Rest Bonus Point"/>
	<String ID="LT_OPERATOR_WARNING_MESSAGE" CONTENT="Send Game Operations Tool Warning"/>
	<String ID="LT_OPERATOR_WARNING_MESSAGE_RECEIVED" CONTENT="Warning from Game Action Tool"/>
	<String ID="LT_OPERATOR_REMOVE_NPC" CONTENT="Remove Game Operation Tool NPC"/>
	<String ID="LT_OPERATOR_KILL_NPC_IN_RANGE" CONTENT="Delete Game Operator Tool Range NPC"/>
	<String ID="LT_USER_REGISTER_LORD_CANDIDATE" CONTENT="Registered Candidate"/>
	<String ID="LT_USER_VOTE_LORD_ELECTION" CONTENT="Vote"/>
	<String ID="LT_USER_WIN_LORD_ELECTION" CONTENT="Lord Elected"/>
	<String ID="LT_USER_DEFEATED_LORD_ELECTION" CONTENT="Failed to be elected lord"/>
	<String ID="LT_LORD_CHANGE_GUARD_TAX" CONTENT="Change Tax Rate"/>
	<String ID="LT_LORD_CHANGE_GUARD_POLICY" CONTENT="Change Policy"/>
	<String ID="LT_LORD_EXTEND_GUARD_POLICY" CONTENT="Extension Policy"/>
	<String ID="LT_LORD_CHANGE_GUARD_NOTICE" CONTENT="Change Protection Notice"/>
	<String ID="LT_LORD_TAKE_GUARD_TAX_PROFIT" CONTENT="Received protection tax"/>
	<String ID="LT_GUARD_POLICY_CHANGED" CONTENT="Change protection policy"/>
	<String ID="LT_ITEM_EXTERIOR_CHANGE" CONTENT="Change item appearance"/>
	<String ID="LT_ITEM_EXTERIOR_RESTORE" CONTENT="Restore item appearance"/>
	<String ID="LT_ITEM_COLORING_CHANGE" CONTENT="Coloring Item"/>
	<String ID="LT_ITEM_COLORING_RESET" CONTENT="Initialize object coloring"/>
	<String ID="Male" CONTENT="Male"/>
	<String ID="Female" CONTENT="Female"/>
	<String ID="Common" CONTENT="Common"/>
	<String ID="IT_BAG" CONTENT="Character Bag"/>
	<String ID="IT_WAREHOUSE" CONTENT="Account Warehouse"/>
	<String ID="IT_PARCEL" CONTENT="Mail"/>
	<String ID="IT_GUILD_WAREHOUSE" CONTENT="Guild Warehouse"/>
	<String ID="IT_VM_SELLER" CONTENT="Vending Machine:Sale"/>
	<String ID="IT_VM_BUYER" CONTENT="Vending Machine:Buy"/>
	<String ID="IT_TRADE_BROKER" CONTENT="Trade Broker"/>
	<String ID="IT_GROUP_DUEL_ITEM_BET" CONTENT="Group Duel Gambling"/>
	<String ID="IT_HOMUN_EQUIPPED" CONTENT="Install Carpet"/>
	<String ID="IT_USER_WAREHOUSE" CONTENT="User Warehouse 1"/>
	<String ID="IT_SEIZURE" CONTENT="Seize"/>
	<String ID="INVTYPE_WEAPON" CONTENT="Weapon"/>
	<String ID="INVTYPE_HEAD" CONTENT="Head"/>
	<String ID="INVTYPE_BODY" CONTENT="Armor"/>
	<String ID="INVTYPE_HANDS" CONTENT="Gloves"/>
	<String ID="INVTYPE_FEET" CONTENT="shoes"/>
	<String ID="INVTYPE_EAR1" CONTENT="Earring (L)"/>
	<String ID="INVTYPE_EAR2" CONTENT="Earring (R)"/>
	<String ID="INVTYPE_FINGER1" CONTENT="Ring (L)"/>
	<String ID="INVTYPE_FINGER2" CONTENT="Ring (R)"/>
	<String ID="INVTYPE_NECKLACE" CONTENT="Necklace"/>
	<String ID="INVTYPE_UNDERWEAR" CONTENT="Underwear"/>
	<String ID="INVTYPE_STYLE_HAIR" CONTENT="Style Hat"/>
	<String ID="INVTYPE_STYLE_FACE" CONTENT="style face"/>
	<String ID="INVTYPE_STYLE_WEAPON" CONTENT="Style Weapon"/>
	<String ID="INVTYPE_STYLE_BACK" CONTENT="Style and other items"/>
	<String ID="INVTYPE_BELT" CONTENT="Belt"/>
	<String ID="INVTYPE_BROOCH" CONTENT="Brooch"/>
	<String ID="QS_FAILED" CONTENT="Failed"/>
	<String ID="QS_PROGRESS" CONTENT="Progress"/>
	<String ID="QS_COMPLETE" CONTENT="Complete"/>
	<String ID="QS_STOP" CONTENT="Stop"/>
	<String ID="EditorMode" CONTENT="Mode"/>
	<String ID="AddNote" CONTENT="Add Note"/>
	<String ID="ModifyNote" CONTENT="Modify Note"/>
	<String ID="AlertMessage" CONTENT="Notice:"/>
	<String ID="InvalidAccountDbId" CONTENT="Invalid Account DbId"/>
	<String ID="InvalidUserName" CONTENT="Invalid User Name"/>
	<String ID="FriendList" CONTENT="Friend List"/>
	<String ID="BlockedList" CONTENT="Blocked List"/>
	<String ID="AddFriend" CONTENT="Add Friend"/>
	<String ID="DelFriend" CONTENT="Delete Friend"/>
	<String ID="DeletedFriend" CONTENT="Deleted Friend"/>
	<String ID="FriendToDelete" CONTENT="Friend to delete"/>
	<String ID="FriendToAdd" CONTENT="Friend To Add"/>
	<String ID="AddedFriend" CONTENT="Added Friend"/>
	<String ID="ConfirmAddFirend" CONTENT="Enter the name of the friend you want to add"/>
	<String ID="ConfirmDeleteFirend" CONTENT="Please check the name of the friend you want to delete"/>
	<String ID="FriendName" CONTENT="Friend's Name"/>
	<String ID="AchievementStr" CONTENT="Achievement"/>
	<String ID="AchievementName" CONTENT="Achievement Name"/>
	<String ID="AchievementID" CONTENT="Achievement ID"/>
	<String ID="AchievementPoint" CONTENT="Score"/>
	<String ID="HaveNotGrade" CONTENT="No Grade"/>
	<String ID="AchievementGrade" CONTENT="Achievement Grade"/>
	<String ID="AchievementGradeToCurrentSeason" CONTENT="Achievement Grade"/>
	<String ID="AchievementGradeToPreSeason" CONTENT="Last Season Grade"/>
	<String ID="DeleteAchievement" CONTENT="Achievement Delete"/>
	<String ID="AchievementToDelete" CONTENT="Achievement to delete"/>
	<String ID="ConfirmDeleteAchievement" CONTENT="Please confirm the achievement to delete"/>
	<String ID="AddArchievement" CONTENT="Add Achievement"/>
	<String ID="ConfirmAddArchievement" CONTENT="Select an achievement to add"/>
	<String ID="AchievementToAdd" CONTENT="Achievement to add"/>
	<String ID="CompleteAchievementInfo" CONTENT="Achievement Information"/>
	<String ID="SearchCompleteAchievement" CONTENT="Search Complete Achievements"/>
	<String ID="GetAchievementPoint" CONTENT="GetAchievementPoint"/>
	<String ID="MaxAchievementPoint" CONTENT="MaxAchievementPoint"/>
	<String ID="GetAchievementTime" CONTENT="GetAchievementTime"/>
	<String ID="AchievementDetail" CONTENT="Achievement Details"/>
	<String ID="AchievementCategory" CONTENT="Achievement Category"/>
	<String ID="ScheduledWorldFestivalViewTitle" CONTENT="[Event Scheduling Status]"/>
	<String ID="EventChoice" CONTENT="Event Choice"/>
	<String ID="EventSearch" CONTENT="Event Search"/>
	<String ID="EventState" CONTENT="Event State"/>
	<String ID="EventStartTime" CONTENT="Event Start Time"/>
	<String ID="EventCurrentSeedCount" CONTENT="Event Current Seed Count"/>
	<String ID="EventSeedCountRecent1Hour" CONTENT="Number of event seeds count in the past hour"/>
	<String ID="EventMaxObjectCount" CONTENT="Maximum number of event monsters in the world at the same time"/>
	<String ID="EventStarted" CONTENT="Started"/>
	<String ID="EventStoped" CONTENT="Stopped"/>
	<String ID="EventStart" CONTENT="Start Event"/>
	<String ID="EventStop" CONTENT="Stop Event"/>
	<String ID="ConfirmEventStart" CONTENT="Confirm Event Start"/>
	<String ID="ConfirmEventStop" CONTENT="Confirm Event Stop"/>
	<String ID="ConfirmEventMaxCurrentSeedCount" CONTENT="View the maximum number of seeds that can exist in the world at the same time"/>
	<String ID="ADD_ACHIEVEMENT" CONTENT="Add Achievement"/>
	<String ID="ADD_ARTISANEXTRACT" CONTENT="Add Extraction Recipe"/>
	<String ID="ADD_FRIEND" CONTENT="Add friend"/>
	<String ID="ADD_ITEM" CONTENT="Add Item"/>
	<String ID="ADD_PRODUCERECIPE" CONTENT="Add Produce Recipe"/>
	<String ID="ADD_SKILL" CONTENT="Add Skill"/>
	<String ID="ADD_USERCREST" CONTENT="Add User Crest"/>
	<String ID="ADD_USERRESTRICTION" CONTENT="ADD USER RESTRICTION"/>
	<String ID="ANNOUNCE" CONTENT="ANNOUNCE"/>
	<String ID="CHANGE_ARTISANPRODUCTION" CONTENT="Change Creation Proficiency"/>
	<String ID="CHANGE_USERPROFS" CONTENT="Change Proficiency"/>
	<String ID="CHANGE_MONEY" CONTENT="Change Money"/>
	<String ID="CHANGE_WARE_MONEY" CONTENT="Change Warehouse Money"/>
	<String ID="DEL_ACHIEVEMENT" CONTENT="Clear achievements"/>
	<String ID="DEL_ARTISANEXTRACT" CONTENT="Delete Extraction Recipe"/>
	<String ID="DEL_FRIEND" CONTENT="Delete Friend"/>
	<String ID="DEL_ITEM" CONTENT="Delete Item"/>
	<String ID="DEL_WAREITEM" CONTENT="Delete Warehouse Item (Style Warehouse)"/>
	<String ID="DEL_SKILL" CONTENT="Delete Skill"/>
	<String ID="DEL_USERCREST" CONTENT="Delete User Crest"/>
	<String ID="DEL_USERRESTRICTION" CONTENT="DELETE USER RESTRICTION"/>
	<String ID="UNDELETE_USER" CONTENT="Restore Deleted User"/>
	<String ID="RENAME_USER" CONTENT="Rename User"/>
	<String ID="DEL_TRADE_BROKER_ITEM" CONTENT="Delete Transaction Broker Item"/>
	<String ID="USER_TELEPORT" CONTENT="Character Teleport"/>
	<String ID="CHANGE_SERVER_SETTING" CONTENT="Change User previous settings"/>
	<String ID="USER_LEVEL" CONTENT="Change User Level"/>
	<String ID="USER_EXP" CONTENT="Change User EXP"/>
	<String ID="REST_BONUS" CONTENT="Change character rest point"/>
	<String ID="CHANGE_GUILD_CHIEF" CONTENT="Change Guild Chapter"/>
	<String ID="DELETE_GUILD" CONTENT="Delete Guild"/>
	<String ID="CHANGE_GUILD_NAME" CONTENT="Rename Guild"/>
	<String ID="CHANGE_GUILD_ANNOUNCE" CONTENT="Change Guild Notification"/>
	<String ID="CHANGE_GUILD_LEVEL" CONTENT="Change Guild Level"/>
	<String ID="CHANGE_GUILD_EXPIRE" CONTENT="Change Guild Expire"/>
	<String ID="CHANGE_CREST_POINT" CONTENT="Change Crest Point"/>
	<String ID="DELETE_GUILD_LOGO" CONTENT="Delete Guild Logo"/>
	<String ID="CHANGE_GUILD_TITLE" CONTENT="Change Guild Title"/>
	<String ID="CHANGE_GUILD_BATTLECHIP" CONTENT="Change Tactical Chip Holding"/>
	<String ID="SEIZURE_ITEM" CONTENT="Sanction Item"/>
	<String ID="RETURNSEIZURE_ITEM" CONTENT="Restore Item"/>
	<String ID="ADD_QUEST" CONTENT="Add Task"/>
	<String ID="CHANGE_QUEST" CONTENT="Modify task"/>
	<String ID="DEL_QUEST" CONTENT="Delete Task"/>
	<String ID="SEIZURE_VENDINGMACHINE" CONTENT="Seize Vending Machine"/>
	<String ID="STOP_STORE" CONTENT="Stop the store"/>
	<String ID="SEIZURE_MONEY" CONTENT="Gold Coin Seizure"/>
	<String ID="RETURNSEIZURE_MONEY" CONTENT="Gold Coin Recovery"/>
	<String ID="SEIZURE_PARCEL" CONTENT="Seize Parcel"/>
	<String ID="DELETE_SEIZURE_ITEM" CONTENT="Delete Seized Item"/>
	<String ID="DELETE_SEIZURE_MONEY" CONTENT="Delete Seized Gold"/>
	<String ID="REQUEST_DISABLE_TERRITORY" CONTENT="Teritory Cabin Settings"/>
	<String ID="DISABLED_TERRITORY_LIST" CONTENT="Find Spawn stop directory"/>
	<String ID="SET_RESPAWN_TIME" CONTENT="Set Rental Time"/>
	<String ID="DUNGEON_NPC_RESPAWN" CONTENT="Npc monitor"/>
	<String ID="DUNGEON_COOLTIME_RESET" CONTENT="Dungeon Cooldown Reset"/>
	<String ID="DungeonEnterCountReset" CONTENT="Reset dungeon entry count"/>
	<String ID="ENABLE_POLITICS" CONTENT="Open Political System"/>
	<String ID="DISABLE_POLITICS" CONTENT="Close the political system"/>
	<String ID="CLEAR_CLIENT_SETTING" CONTENT="Delete ClientSetting"/>
	<String ID="TASK_CONFIRM" CONTENT="TASK execute request"/>
	<String ID="TASK_CANCEL" CONTENT="TASK Rejection Request"/>
	<String ID="CHANGE_GUARD_NOTICE" CONTENT="Change Protection Notice"/>
	<String ID="CHANGE_GUARD_POLICY_POINT" CONTENT="Change reserved policy points"/>
	<String ID="CHANGE_GUARD_TAX_RATE" CONTENT="Change Protection Rate"/>
	<String ID="CHANGE_GUARD_POLICY" CONTENT="Change protection policy"/>
	<String ID="CHANGE_LORD_COMPETITION_POINT" CONTENT="Change Permanent Resident Candidate Competitive Points"/>
	<String ID="DISMISS_LORD" CONTENT="Forced Dismissal of Permanent Residence"/>
	<String ID="CHANGE_GUARD_TAX" CONTENT="Change Guard Tax"/>
	<String ID="RESET_VOTE" CONTENT="Remove Voting History"/>
	<String ID="RESET_COLORING" CONTENT="Initialize Coloring"/>
	<String ID="RESET_EXTERIOR" CONTENT="Initialize shape change"/>
	<String ID="RESET_WARE_COLORING" CONTENT="Initialize Coloring"/>
	<String ID="RESET_WARE_EXTERIOR" CONTENT="Initialize shape change"/>
	<String ID="CHANGE_LORD_COMPETITION_PLEDGE" CONTENT="Change Permanent Resident Candidate Commitment"/>
	<String ID="TOGGLE_ARTISAN_BOOKMARK" CONTENT="Switch Recipe Favorites"/>
	<String ID="SET_WORLD_FESTIVAL" CONTENT="Set World Events"/>
	<String ID="SET_WORLD_FESTIVAL_PARAM" CONTENT="Change the number of objects for concurrent events"/>
	<String ID="SET_WORLD_FESTIVAL_PARAM_LIST" CONTENT="Set World Events Parameters List"/>
	<String ID="LEFT_TIME_DAY_HOUR" CONTENT="{0}days"/>
	<String ID="LEFT_TIME_DAY" CONTENT="{0}days"/>
	<String ID="LEFT_TIME_HOUR_MIN" CONTENT="minutes"/>
	<String ID="LEFT_TIME_HOUR" CONTENT="{0}hour"/>
	<String ID="LEFT_TIME_MIN" CONTENT="{0}minutes"/>
	<String ID="LEFT_TIME_INFINITE" CONTENT="Permanent Sanctions"/>
	<String ID="TargetTime" CONTENT="Target Time"/>
	<String ID="StatrTime" CONTENT="Start Time"/>
	<String ID="Time_CompleteTime" CONTENT="Complete Time"/>
	<String ID="Immediate" CONTENT="Immediate"/>
	<String ID="Year" CONTENT="Year"/>
	<String ID="Month" CONTENT="Month"/>
	<String ID="Day" CONTENT="Day"/>
	<String ID="Hour" CONTENT="Hour"/>
	<String ID="Minute" CONTENT="Minute"/>
	<String ID="TimeStamp" CONTENT="Time"/>
	<String ID="Forever" CONTENT="Forever"/>
	<String ID="SearchReport" CONTENT="Search Report"/>
	<String ID="LogType" CONTENT="Log Type"/>
	<String ID="ViewSelectedServer" CONTENT="Find on Selected Server"/>
	<String ID="ViewMergeServer" CONTENT="Find on Integration Server"/>
	<String ID="ViewLogTypeSelectedServer" CONTENT="Find by type on selected server"/>
	<String ID="ViewLogTypeMergeServer" CONTENT="Find by Type on Integration Server"/>
	<String ID="ReportName" CONTENT="Report Type"/>
	<String ID="ReportDesc" CONTENT="Report Description"/>
	<String ID="TopDelItemPriceHelp" CONTENT="Option to see the duration for which item value disappears\nSee who deleted the item with the most value\nThe value of selling the item to the shop or discarding it on the land (based on buyPrice in itemTemplate)\n If you discard two items with a buyPrice of 100, the 200 loses its value and -200 is recorded"/>
	<String ID="CastSkillHelp" CONTENT="Select skill usage query duration\nSee which skills Carrick uses frequently"/>
	<String ID="CharCreateByClassHelp" CONTENT="Class-specific number of creations"/>
	<String ID="CharCreateByRaceHelp" CONTENT="Number of creations per race"/>
	<String ID="CharDeleteByClassHelp" CONTENT="Class-specific deletes"/>
	<String ID="CharDeleteByRaceHelp" CONTENT="Delete by Race"/>
	<String ID="ConnectionsHelp" CONTENT="Select sync lookup duration\nReport average/min/max syncs in one hour"/>
	<String ID="CurrentBodyItemHelp" CONTENT="Select the time to view the items equipped by the character\nRecord the items equipped by the character every 5 minutes\nThe most equipped 1000 body defenses at that time"/>
	<String ID="CurrentCharHelp" CONTENT="Choose how long to view the character's stay\nView by the character location log left every 5 minutes\nThe higher value is where many characters are active in the world"/>
	<String ID="CurrentCharTopHelp" CONTENT="Find the character's most frequent location selection duration\nUse the character's location log left every 5 minutes\nFind the character's most frequent 100 locations"/>
	<String ID="CurrentWeaponItemHelp" CONTENT="Select the item equipped by the character. The query time will record the weapon items equipped by the character every 5 minutes\nThe most equipped weapon items will be 1000"/>
	<String ID="EarnExpLocHelp" CONTENT="Choose where to get experience points and query time period\nShow where to get more experience points\nThe places with high numbers are where many characters get more experience points"/>
	<String ID="EarnExpLocTopHelp" CONTENT="Browse the character's most frequently gained experience to select the duration\nview the character's most frequently gained experience log\nview the character's most frequently gained 100 experience"/>
	<String ID="GetItemHelp" CONTENT="Choose an item acquisition query period\nSee which items are acquired the most\nThis is the number of acquired items, including finding items in the warehouse or buying items in the store"/>
	<String ID="HuntingNpcLocHelp" CONTENT="Select the location where NPC hunts to check the time period\nShow where NPCs hunt more\nThe places with higher numbers are places where many characters hunt more NPCs"/>
	<String ID="HuntingNpcLocTopHelp" CONTENT="Browse the places where characters are most often hunted by NPCs to select the duration\nBrowse the logs of characters most frequently hunted by NPCs\nBrowse 100 places where characters are most frequently hunted by NPCs"/>
	<String ID="KillNPCCountClassLevelHelp" CONTENT="Check the number of character hunting NPCs\nCheck the number of all character hunting NPCs of a specific class and level in a day"/>
	<String ID="KillNPCCountLevelHelp" CONTENT="Check the number of character hunting NPCs\nCheck the number of all character hunting NPCs of a specific level in a day"/>
	<String ID="KillNPCEarnExpHelp" CONTENT="How much experience is gained by hunting NPCs\nHow much experience a character will gain by hunting NPCs at this level"/>
	<String ID="KillNPCEarnExpClassLevelHelp" CONTENT="How much experience is gained from hunting NPCs\nHow much experience does the character gain from hunting NPCs at this level of this class"/>
	<String ID="LevelUpClassLevelHelp" CONTENT="Select role upgrade query duration\nQuery for each class the time it took for a role to upgrade from a level"/>
	<String ID="LevelUpLevelHelp" CONTENT="Select the duration of the role upgrade query\nQuery the time it takes for a role to upgrade a level"/>
	<String ID="NpcDeadHelp" CONTENT="Check how many NPCs died select time\nCheck how many NPCs died"/>
	<String ID="NpcDeadPcClassHelp" CONTENT="Check how many times the NPC died. Select the time\n How many times the NPC died. Press the class of the killed PC to see how many times the npc was killed by which PC class"/>
	<String ID="NpcDropItemHelp" CONTENT="Select NPC Drop Item to see the duration\nCheck how many times NPCs drop something"/>
	<String ID="PcDeadByNPCClassLevelHelp" CONTENT="Select the query period when the character died from NPC\nView the number of times the character died from NPCs\nCount and display the number of times the character died by class and level"/>
	<String ID="PcDeadByNPCLevelHelp" CONTENT="Select the character death query period\nCheck how many characters were killed by NPCs\nCount and show how many times each dead character's level died"/>
	<String ID="PcDeadByPcClassLevelHelp" CONTENT="Select the character death query period\nView the number of times the character was killed by other characters\nCount and display the number of dead characters by class and level"/>
	<String ID="PcDeadByPcLevelHelp" CONTENT="Select the duration of the character death query\nView the number of times the character was killed by other characters\nCount and show how many times the dead character died in each level"/>
	<String ID="TopDelMoneyHelp" CONTENT="Select the viewing period for which money disappears\nSee who has removed the most money\nThe sum of money disappearing from a character's inventory in any way"/>
	<String ID="TopDeltaItemPriceHelp" CONTENT="Select the time to view the item value change\nSee whose item value has changed the most\nWhether you buy an item from the store or recycle an item from the store, the value of the item in the character's inventory (based on itemTemplate's buyPrice)\nIf you buy two items with a buyPrice of 100, then your value is 200, so 200 is recorded\nIf you read the value of 100 and get 200, then your value becomes became 100"/>
	<String ID="TopDeltaMoneyHelp" CONTENT="Select the viewing period for which money disappears\nSee who reads the most money\nThe sum of money disappearing from a character's inventory in any way"/>
	<String ID="TopGetItemPriceHelp" CONTENT="Select the query period to get the item value\nSee who gets the most item value\nWhether buying the item from the store or recycling it from the store, enter the item value of the character's inventory (based on itemTemplate buyPrice)\nIf you buy two items with a buyPrice of 100, you get 200 worth of cash, so we get 200"/>
	<String ID="TopGetMoneyHelp" CONTENT="Select a query period for getting money\nSee who gets the most money\nAmount of money earned from a character's inventory in any way"/>
	<String ID="UseItemHelp" CONTENT="Select an item to use to view the duration\nSee which item is most frequently used"/>
	<String ID="AverageActiveUser" CONTENT="Average Player"/>
	<String ID="AverageConnectRectriction" CONTENT="Average Connect Restriction"/>
	<String ID="DeleteCount" CONTENT="Delete Count"/>
	<String ID="DeleteItemCount" CONTENT="Number of deleted items"/>
	<String ID="DeleteItemValue" CONTENT="Delete ItemValue Sum"/>
	<String ID="DeleteTotalCount" CONTENT="DeleteTotal"/>
	<String ID="DeltaCount" CONTENT="Number of changes"/>
	<String ID="DeltaItemCount" CONTENT="Variable item count"/>
	<String ID="DeltaItemValue" CONTENT="Variable item value sum"/>
	<String ID="DeltaTotalCount" CONTENT="Variable Total"/>
	<String ID="Report_GetCount" CONTENT="Get count"/>
	<String ID="GetItemCount" CONTENT="Get item count"/>
	<String ID="GetItemValue" CONTENT="Total item value obtained"/>
	<String ID="GetTotalCount" CONTENT="Get Total"/>
	<String ID="DropCount" CONTENT="DropCount"/>
	<String ID="DropTotalCount" CONTENT="DropTotalCount"/>
	<String ID="CreateCount" CONTENT="Create count"/>
	<String ID="UseCount" CONTENT="Use count"/>
	<String ID="UseSkillName" CONTENT="Use Skill Name"/>
	<String ID="UseItemName" CONTENT="Use ItemName"/>
	<String ID="EquippedItemName" CONTENT="Installation item name"/>
	<String ID="FrequentlyCount" CONTENT="Frequently Count"/>
	<String ID="UserLogCount" CONTENT="User Log Count"/>
	<String ID="DeadNPCName" CONTENT="Dead NPC Name"/>
	<String ID="KillerPCClass" CONTENT="Class of Killed PC"/>
	<String ID="DeadTotalCount" CONTENT="DeadTotal"/>
	<String ID="TotalExp" CONTENT="Get total experience"/>
	<String ID="NPCHuntCount" CONTENT="NPC hunt count"/>
	<String ID="KillNPCCountClassLevel" CONTENT="Number of NPCs hunted by characters of the same level in one day"/>
	<String ID="KillNpcCountLevel" CONTENT="Number of NPCs hunted by characters of the same level in one day"/>
	<String ID="DailyExpCount" CONTENT="Number of exp gained in a day"/>
	<String ID="TotalDailyExp" CONTENT="Experience gained in one day"/>
	<String ID="AverageExp" CONTENT="Average EXP gained once"/>
	<String ID="LevelUpPreLevel" CONTENT="Level Up Level"/>
	<String ID="LevelUpTime" CONTENT="Level Up Level"/>
	<String ID="LevelUpCount" CONTENT="Total upgrades"/>
	<String ID="AverageLevelUpSec" CONTENT="Average LevelUpSec in seconds"/>
	<String ID="AverageLevelUpMin" CONTENT="Average level up time (minutes)"/>
	<String ID="ConfirmDoTask" CONTENT="Check items to run"/>
	<String ID="ConfirmCancelTask" CONTENT="Confirm the item to be rejected"/>
	<String ID="DoTaskButton" CONTENT="Run"/>
	<String ID="CancelTaskButton" CONTENT="Cancel"/>
	<String ID="TASK_Request" CONTENT="Task run request"/>
	<String ID="CancelRequest" CONTENT="Task cancel request"/>
	<String ID="TaskConfirm" CONTENT="Verification Step"/>
	<String ID="TaskCancel" CONTENT="Rejected"/>
	<String ID="TaskNoError" CONTENT="Executed"/>
	<String ID="TaskCannotSend" CONTENT="Server Transmission Error"/>
	<String ID="TaskAfterSend" CONTENT="Server listening error"/>
	<String ID="TaskAfterRecv" CONTENT="Execution error"/>
	<String ID="FailedTask" CONTENT="Failed to execute command"/>
	<String ID="SuccessTask" CONTENT="Command executed successfully"/>
	<String ID="SearchTask" CONTENT="View results"/>
	<String ID="TASK_Search" CONTENT="Task Search"/>
	<String ID="SearchPage" CONTENT="Search Page"/>
	<String ID="SearchedTaskId" CONTENT="Search Task ID"/>
	<String ID="MovedCharHolding" CONTENT="MovedCharHolding"/>
	<String ID="QuestClearCount" CONTENT="Time Complete"/>
	<String ID="BattleFieldServerStatus" CONTENT="BattleFieldServerStatus"/>
	<String ID="BattleFieldUiOnOff" CONTENT="BattleFieldUiOnOff"/>
	<String ID="TradeOpponent" CONTENT="TradeOpponent"/>
	<String ID="IsTradeRequestor" CONTENT="Whether to request a trade"/>
	<String ID="LT_VENDING_DESTROY_ITEM" CONTENT="Dead Pat carries items"/>
	<String ID="LT_VENDING_DESTROY_MONEY" CONTENT="Death's Hijacking Gold"/>
	<String ID="SelectAll" CONTENT="Select All"/>
	<String ID="TaskRequestor" CONTENT="Operation Requestor"/>
	<String ID="TaskConfirmer" CONTENT="Operation Confirmer"/>
	<String ID="InvalidCrestParent" CONTENT="Remove the sentence you want to add with the same effect and try again"/>
	<String ID="LOGINFO_UserLoginDesc" CONTENT="Log left by the user when logging in."/>
	<String ID="LOGINFO_UserDbId" CONTENT="User DBID"/>
	<String ID="LOGINFO_UserLevel" CONTENT="User Level"/>
	<String ID="LOGINFO_UserClass" CONTENT="User Class"/>
	<String ID="LOGINFO_UserRace" CONTENT="User Race"/>
	<String ID="LOGINFO_UserGender" CONTENT="User Gender"/>
	<String ID="LOGINFO_UserName" CONTENT="Username"/>
	<String ID="LOGINFO_AccountName" CONTENT="Account Name"/>
	<String ID="LOGINFO_AccountDbId" CONTENT="Account DBID"/>
	<String ID="LOGINFO_UserLogOutDesc" CONTENT="Log left when the user logs out."/>
	<String ID="LOGINFO_UserPlaySec" CONTENT="Play time (seconds) during login to logout"/>
	<String ID="LOGINFO_LordElectionGeneration" CONTENT="Generation Number"/>
	<String ID="LOGINFO_ContinentId" CONTENT="Continent ID"/>
	<String ID="LOGINFO_GuardId1" CONTENT="First Guard"/>
	<String ID="LOGINFO_GuardId2" CONTENT="Second Guard"/>
	<String ID="LOGINFO_GuardId3" CONTENT="Third guard"/>
	<String ID="LOGINFO_GuardId4" CONTENT="Fourth Guard"/>
	<String ID="LOGINFO_GuardId5" CONTENT="Fifth Guard"/>
	<String ID="LOGINFO_ElectionType" CONTENT="Election Method"/>
	<String ID="LOGINFO_CandidateName" CONTENT="Selected Candidate Name"/>
	<String ID="LOGINFO_RegisterLordCandidateDesc" CONTENT="Logs left when registering permanent resident candidates"/>
	<String ID="LOGINFO_VoteLordElectionDesc" CONTENT="Logs recorded when voting for a candidate for the post of ruler of the lands"/>
	<String ID="LOGINFO_WinLordElectionDesc" CONTENT="The log left by Yongju when he was elected"/>
	<String ID="LOGINFO_DefeatedElectionDesc" CONTENT="The log left when the Yeongju election was lost"/>
	<String ID="LOGINFO_VotePoint" CONTENT="Number of votes"/>
	<String ID="LOGINFO_GuardName" CONTENT="Guard Name"/>
	<String ID="LOGINFO_GuardId" CONTENT="Guard ID"/>
	<String ID="LOGINFO_PolicyId" CONTENT="Policy ID"/>
	<String ID="LOGINFO_TaxCategory" CONTENT="Tax Category"/>
	<String ID="LOGINFO_TaxOldRate" CONTENT="Old Tax Rate"/>
	<String ID="LOGINFO_TaxNewRate" CONTENT="Tax Rate Changed"/>
	<String ID="LOGINFO_PolicyItemId" CONTENT="PolicyId"/>
	<String ID="LOGINFO_PolicyOldItemId" CONTENT="Old Policy Id"/>
	<String ID="LOGINFO_PolicyNewItemId" CONTENT="Changed Policy Id"/>
	<String ID="LOGINFO_ChangeGuardTaxDesc" CONTENT="Log left when guard tax rates are changed"/>
	<String ID="LOGINFO_ChangeGuardPolicyDesc" CONTENT="Log left when guard policy is changed"/>
	<String ID="LOGINFO_ExtendGuardPolicyDesc" CONTENT="Log left when guard policy changed"/>
	<String ID="LOGINFO_ChangeGuardNoticeDesc" CONTENT="log left when guard guide changed"/>
	<String ID="LOGINFO_Notice" CONTENT="Notice"/>
	<String ID="LOGINFO_TaxProfit" CONTENT="Tax income"/>
	<String ID="LOGINFO_TaxProfitRecord" CONTENT="Total tax revenue"/>
	<String ID="LOGINFO_TakeGuardTaxProfitDesc" CONTENT="Log left when taking guard tax income"/>
	<String ID="LOGINFO_ChangedPolicyPointDesc" CONTENT="Log left when guard policy points are changed"/>
	<String ID="LOGINFO_LordDbId" CONTENT="Lord's DbId"/>
	<String ID="LOGINFO_PolicyPointOld" CONTENT="Last Policy Point"/>
	<String ID="LOGINFO_PolicyPointnew" CONTENT="Changed Policy Point"/>
	<String ID="LOGINFO_UserPayTaxDesc" CONTENT="Logs left by user when paying taxes"/>
	<String ID="LOGINFO_NpcHuntingZone" CONTENT="NPC's HuntingZone Id"/>
	<String ID="LOGINFO_NpcTemplateId" CONTENT="Template Id of NPC"/>
	<String ID="LOGINFO_Tax" CONTENT="Tax"/>
	<String ID="LOGINFO_TaxofLord" CONTENT="Lord's Share"/>
	<String ID="LOGINFO_TaxofConsul" CONTENT="Consul's share"/>
	<String ID="LOGINFO_TaxofOtherslord" CONTENT="Other lord's share"/>
	<String ID="LOGINFO_GuardCalculateTaxDesc" CONTENT="Logs left when tax savings are divided into lords, consuls and other lords"/>
	<String ID="LOGINFO_GuardTaxProfitParcelDesc" CONTENT="The log left when the remaining tax was handed over to the lord as a parcel after Garde's reign ended"/>
	<String ID="LOGINFO_WeaponTemplateId" CONTENT="WeaPon TemplateId"/>
	<String ID="LOGINFO_BodyTemplateId" CONTENT="Body TemplateId"/>
	<String ID="LOGINFO_HandsTemplateId" CONTENT="HandsTemplateId"/>
	<String ID="LOGINFO_FeetTemplateId" CONTENT="Feet TemplateId"/>
	<String ID="LOGINFO_Ear1TemplateId" CONTENT="Ear1TemplateId"/>
	<String ID="LOGINFO_Ear2TemplateId" CONTENT="Ear2 TemplateId"/>
	<String ID="LOGINFO_Finger1TemplateId" CONTENT="finger1TemplateId"/>
	<String ID="LOGINFO_Finger2TemplateId" CONTENT="finger2TemplateId"/>
	<String ID="LOGINFO_EquipmentsUpdateDesc" CONTENT="Users leave in the log the equipment they are using every once in a while"/>
	<String ID="LOGINFO_AccessoryUpdateDesc" CONTENT="Users keep the jewelry they wear in the log at regular intervals"/>
	<String ID="LOGINFO_WeaponCostomUpdateDesc" CONTENT="Users will leave in the log the crystals they are wearing to strengthen Weapons at regular intervals"/>
	<String ID="LOGINFO_BodyCustomUpdateDesc" CONTENT="Users will leave the Body Enhancement Crystal they are wearing in the log at regular intervals"/>
	<String ID="LOGINFO_Slot0" CONTENT="Slot 0"/>
	<String ID="LOGINFO_Slot1" CONTENT="Slot 1"/>
	<String ID="LOGINFO_Slot2" CONTENT="Slot 2"/>
	<String ID="LOGINFO_Slot3" CONTENT="Slot 3"/>
	<String ID="LOGINFO_Slot4" CONTENT="Slot 4"/>
	<String ID="LOGINFO_UserPlaySecond" CONTENT="Online Time"/>
	<String ID="LOGINFO_UserBattleSecond" CONTENT="Battle Time"/>
	<String ID="LOGINFO_UserPartySecond" CONTENT="Party Time"/>
	<String ID="LOGINFO_UserMoveCommandCount" CONTENT="Number of move commands"/>
	<String ID="LOGINFO_UserSkillCommandCount" CONTENT="SkillCommandCount"/>
	<String ID="LOGINFO_UserMoveDistanceSum" CONTENT="Total Moved Distance"/>
	<String ID="LOGINFO_UserMoveActionDesc" CONTENT="Users leave a log of movement distance and actions at regular intervals"/>
	<String ID="LOGINFO_CampTelePortDesc" CONTENT="Logs left by the user when using the camera for the TV port"/>
	<String ID="LOGINFO_TelePortDesc" CONTENT="User's log on TV"/>
	<String ID="LOGINFO_ChannelId" CONTENT="ChannelId"/>
	<String ID="LOGINFO_IntX" CONTENT="(Int) X coordinate"/>
	<String ID="LOGINFO_IntY" CONTENT="(Int) Y coordinate"/>
	<String ID="LOGINFO_IntZ" CONTENT="(Int) Z coordinate"/>
	<String ID="LOGINFO_BeginPegasus" CONTENT="Log left when Pegasus starts"/>
	<String ID="LOGINFO_EndPegasus" CONTENT="Log left at the end of Pegasus"/>
	<String ID="LOGINFO_AddFriendDesc" CONTENT="Log when user *adds friend*"/>
	<String ID="LOGINFO_DeleteFriendDesc" CONTENT="Log left when user *deleted* friend"/>
	<String ID="LOGINFO_AddFriendDbId" CONTENT="DbId of friend to add"/>
	<String ID="LOGINFO_AddFriendName" CONTENT="Name of friend to add"/>
	<String ID="LOGINFO_BeforeChannel" CONTENT="Old Channel"/>
	<String ID="LOGINFO_AfterChannel" CONTENT="Changed channel"/>
	<String ID="LOGINFO_UserChangeChannelDesc" CONTENT="Logs recorded when the user changes the channel"/>
	<String ID="LOGINFO_UserTelePortAcceptDesc" CONTENT="Log of successful (accepted) teleport by user"/>
	<String ID="LOGINFO_UserTelePortRejectDesc" CONTENT="Log left when user teleport failed (rejected)"/>
	<String ID="LOGINFO_NearTownEscapeDesc" CONTENT="Logs left by the user when they made an emergency escape and moved to a nearby city"/>
	<String ID="LOGINFO_HOMUNIncubatorStartDesc" CONTENT="Humen Kulus Incubation *Start*Remaining Log"/>
	<String ID="LOGINFO_HOMUNIncubatorCompleteDesc" CONTENT="Humen Kulus Incubation *Success*Remaining Log"/>
	<String ID="LOGINFO_HOMUNIncubatorSuccesDesc" CONTENT="Log left when Humen Kulus successfully hatched to generate props"/>
	<String ID="LOGINFO_HOMUNIncubatorFaillDesc" CONTENT="Log left when Humen Kurus failed to hatch and generate props"/>
	<String ID="LOGINFO_HOMUNItemTemplateId" CONTENT="Homunculus ItemTemplateId"/>
	<String ID="LOGINFO_HOMUNOrbItemTemplateId" CONTENT="Id"/>
	<String ID="LOGINFO_HOMUNPetTemplateId" CONTENT="PetId"/>
	<String ID="LOGINFO_HOMUNSuccessItemTemplateId" CONTENT="Id of the item generated after successful incubation"/>
	<String ID="LOGINFO_HOMUNFaillItemTemplateId" CONTENT="Id of the item generated after successful incubation"/>
	<String ID="LOGINFO_WarningSent" CONTENT="Send warning"/>
	<String ID="LOGINFO_WarningReceived" CONTENT="Received alert"/>
	<String ID="LOGINFO_WarningTargetLabel" CONTENT="Target User"/>
	<String ID="LOGINFO_WarningSenderLabel" CONTENT="Send User"/>
	<String ID="LOGINFO_NpcRemoved" CONTENT="Remove NPC"/>
	<String ID="LOGINFO_NpcRemovedLabel" CONTENT="Remove NPC"/>
	<String ID="LOGINFO_NpcRemovedInRange" CONTENT="Remove Range NPC"/>
	<String ID="LOGINFO_NpcRemovedInRangeLabel" CONTENT="Remove Range"/>
	<String ID="LOGINFO_NpcSpawn" CONTENT="NPC summon"/>
	<String ID="LT_TS_IDENTIFY_ITEM" CONTENT="Unblock item"/>
	<String ID="LT_TS_UNIDENTIFY_ITEM" CONTENT="Reseal item"/>
	<String ID="LT_REMOVE_ITEM_CURSE" CONTENT="Cancel item curse"/>
	<String ID="SEIZURE_TRADE_BROKER_ITEM" CONTENT="Seize Trade Broker Item"/>
	<String ID="TradeBrokerStatus" CONTENT="Trade Broker Sales Status"/>
	<String ID="TradeBrokerStatusWaiting" CONTENT="For Sale"/>
	<String ID="TradeBrokerStatusBought" CONTENT="Waiting for purchase settlement"/>
	<String ID="ConfirmSeizureTradeBrokerItem" CONTENT="Confirm the item to be seized"/>
	<String ID="NAVMENU_FirstPage_Short" CONTENT="Platform Homepage"/>
	<String ID="NAVMENU_FirstPage_Full" CONTENT="First"/>
	<String ID="NAVMENU_add_achievement_Short" CONTENT="Add achievement"/>
	<String ID="NAVMENU_add_achievement_Full" CONTENT="Add achievement"/>
	<String ID="NAVMENU_add_artisanextract_Short" CONTENT="Add item extraction"/>
	<String ID="NAVMENU_add_artisanextract_Full" CONTENT="Add Extract"/>
	<String ID="NAVMENU_add_friend_Short" CONTENT="Add Friend"/>
	<String ID="NAVMENU_add_friend_Full" CONTENT="Add Friend"/>
	<String ID="NAVMENU_add_item_Short" CONTENT="Add item"/>
	<String ID="NAVMENU_add_item_Full" CONTENT="Add item"/>
	<String ID="NAVMENU_add_producerecipe_Short" CONTENT="Add Authoring Template"/>
	<String ID="NAVMENU_add_producerecipe_Full" CONTENT="Add Authoring Template"/>
	<String ID="NAVMENU_add_skill_Short" CONTENT="Add Skill"/>
	<String ID="NAVMENU_add_skill_Full" CONTENT="Add Skill"/>
	<String ID="NAVMENU_add_usercrest_Short" CONTENT="Add Statement"/>
	<String ID="NAVMENU_add_usercrest_Full" CONTENT="Add Statement"/>
	<String ID="NAVMENU_add_userrestriction_Short" CONTENT="Add Sanction"/>
	<String ID="NAVMENU_add_userrestriction_Full" CONTENT="Add Sanction"/>
	<String ID="NAVMENU_change_artisanProduction_Short" CONTENT="Change Manufacturing Proficiency"/>
	<String ID="NAVMENU_change_artisanProduction_Full" CONTENT="Change Manufacturing Proficiency"/>
	<String ID="NAVMENU_change_userProfs_Short" CONTENT="Change Proficiency"/>
	<String ID="NAVMENU_change_userProfs_Full" CONTENT="Change Proficiency"/>
	<String ID="NAVMENU_change_money_Short" CONTENT="Change Inventon"/>
	<String ID="NAVMENU_change_money_Full" CONTENT="Change Inventon"/>
	<String ID="NAVMENU_change_waremoney_Short" CONTENT="Change warehouse value"/>
	<String ID="NAVMENU_change_waremoney_Full" CONTENT="Change warehouse value"/>
	<String ID="NAVMENU_del_achievement_Short" CONTENT="Delete Achievement"/>
	<String ID="NAVMENU_del_achievement_Full" CONTENT="Delete Achievement"/>
	<String ID="NAVMENU_del_artisanExtract_Short" CONTENT="Delete item extraction"/>
	<String ID="NAVMENU_del_artisanExtract_Full" CONTENT="Delete Extract"/>
	<String ID="NAVMENU_del_friend_Short" CONTENT="Delete Friend"/>
	<String ID="NAVMENU_del_friend_Full" CONTENT="Delete Friend"/>
	<String ID="NAVMENU_del_item_Short" CONTENT="Delete item"/>
	<String ID="NAVMENU_del_item_Full" CONTENT="Delete Item"/>
	<String ID="NAVMENU_del_wareItem_Short" CONTENT="Delete Warehouse Item"/>
	<String ID="NAVMENU_del_wareItem_Full" CONTENT="Delete Warehouse Item"/>
	<String ID="NAVMENU_del_skill_Short" CONTENT="Delete Skill"/>
	<String ID="NAVMENU_del_skill_Full" CONTENT="Delete Skill"/>
	<String ID="NAVMENU_del_userCrest_Short" CONTENT="Delete Crest"/>
	<String ID="NAVMENU_del_userCrest_Full" CONTENT="Delete Crest"/>
	<String ID="NAVMENU_del_userRestriction_Short" CONTENT="Delete Sanction"/>
	<String ID="NAVMENU_del_userRestriction_Full" CONTENT="Delete User Restriction"/>
	<String ID="NAVMENU_undelete_user_Short" CONTENT="Restore User"/>
	<String ID="NAVMENU_undelete_user_Full" CONTENT="Restore Deleted User"/>
	<String ID="NAVMENU_rename_user_Short" CONTENT="User Rename"/>
	<String ID="NAVMENU_rename_user_Full" CONTENT="User Rename"/>
	<String ID="NAVMENU_del_trade_broker_item_Short" CONTENT="Delete Auction"/>
	<String ID="NAVMENU_del_trade_broker_item_Full" CONTENT="Delete Auction"/>
	<String ID="NAVMENU_user_teleport_Short" CONTENT="TV port"/>
	<String ID="NAVMENU_user_teleport_Full" CONTENT="TV port"/>
	<String ID="NAVMENU_change_server_setting_Short" CONTENT="Change server configuration"/>
	<String ID="NAVMENU_change_server_setting_Full" CONTENT="Change server configuration"/>
	<String ID="NAVMENU_user_level_Short" CONTENT="Change Level"/>
	<String ID="NAVMENU_user_level_Full" CONTENT="Change User Level"/>
	<String ID="NAVMENU_user_exp_Short" CONTENT="Change EXP"/>
	<String ID="NAVMENU_user_exp_Full" CONTENT="Change character experience value"/>
	<String ID="NAVMENU_change_guild_chief_Short" CONTENT="Change Guild Field"/>
	<String ID="NAVMENU_change_guild_chief_Full" CONTENT="Change Guild Field"/>
	<String ID="NAVMENU_delete_guild_Short" CONTENT="Delete Guild"/>
	<String ID="NAVMENU_delete_guild_Full" CONTENT="Delete Guild"/>
	<String ID="NAVMENU_change_guild_name_Short" CONTENT="Guild Rename"/>
	<String ID="NAVMENU_change_guild_name_Full" CONTENT="Rename Guild"/>
	<String ID="NAVMENU_change_guild_announce_Short" CONTENT="Change Guild Announcement"/>
	<String ID="NAVMENU_change_guild_announce_Full" CONTENT="Change Guild Announcement"/>
	<String ID="NAVMENU_change_guild_level_Short" CONTENT="Change Guild Level"/>
	<String ID="NAVMENU_change_guild_level_Full" CONTENT="Change Guild Level"/>
	<String ID="NAVMENU_change_guild_expire_Short" CONTENT="Change Guild Level Retention Time"/>
	<String ID="NAVMENU_change_guild_expire_Full" CONTENT="Change Guild Level Retention Time"/>
	<String ID="NAVMENU_change_crest_point_Short" CONTENT="Change statement point"/>
	<String ID="NAVMENU_change_crest_point_Full" CONTENT="Change statement point"/>
	<String ID="NAVMENU_delete_guild_logo_Short" CONTENT="Delete Guild Logo"/>
	<String ID="NAVMENU_delete_guild_logo_Full" CONTENT="Delete Guild Logo"/>
	<String ID="NAVMENU_change_guild_title_Short" CONTENT="Change Guild Title"/>
	<String ID="NAVMENU_change_guild_title_Full" CONTENT="Change Guild Title"/>
	<String ID="NAVMENU_UserMemoDetail_Short" CONTENT="View annotation details"/>
	<String ID="NAVMENU_UserMemoDetail_Full" CONTENT="View note details such as account notes or restrictions"/>
	<String ID="NAVMENU_AccountMemoDetail_Short" CONTENT="View note details"/>
	<String ID="NAVMENU_AccountMemoDetail_Full" CONTENT="View note details"/>
	<String ID="NAVMENU_sndan_Short" CONTENT="Notice"/>
	<String ID="NAVMENU_sndan_Full" CONTENT="Notification"/>
	<String ID="NAVMENU_seizure_item_Short" CONTENT="Seizure item"/>
	<String ID="NAVMENU_seizure_item_Full" CONTENT="Seizure item"/>
	<String ID="NAVMENU_returnseizure_item_Short" CONTENT="Return Seizure Item"/>
	<String ID="NAVMENU_returnseizure_item_Full" CONTENT="Return Seizure Item"/>
	<String ID="NAVMENU_add_quest_Short" CONTENT="Add Task"/>
	<String ID="NAVMENU_add_quest_Full" CONTENT="Add Task"/>
	<String ID="NAVMENU_change_quest_Short" CONTENT="Modify task"/>
	<String ID="NAVMENU_change_quest_Full" CONTENT="Modify Task"/>
	<String ID="NAVMENU_del_quest_Short" CONTENT="Clear Task"/>
	<String ID="NAVMENU_del_quest_Full" CONTENT="Clear Task"/>
	<String ID="NAVMENU_seizure_vendingmachine_Short" CONTENT="Seizure vending machine"/>
	<String ID="NAVMENU_seizure_vendingmachine_Full" CONTENT="Seizure vending machine"/>
	<String ID="NAVMENU_stop_store_Short" CONTENT="Store Stopped"/>
	<String ID="NAVMENU_stop_store_Full" CONTENT="Store discontinued"/>
	<String ID="NAVMENU_seizure_money_Short" CONTENT="Gold Seizure"/>
	<String ID="NAVMENU_seizure_money_Full" CONTENT="Gold Seizure"/>
	<String ID="NAVMENU_returnseizure_money_Short" CONTENT="Confiscation of Gold"/>
	<String ID="NAVMENU_returnseizure_money_Full" CONTENT="Forfeit money for redemption"/>
	<String ID="NAVMENU_add_achievement_request_only_Short" CONTENT="Add Achievement"/>
	<String ID="NAVMENU_add_achievement_request_only_Full" CONTENT="Add Achievement"/>
	<String ID="NAVMENU_add_artisanextract_request_only_Short" CONTENT="Add item"/>
	<String ID="NAVMENU_add_artisanextract_request_only_Full" CONTENT="Add Extract"/>
	<String ID="NAVMENU_add_friend_request_only_Short" CONTENT="Add Friend"/>
	<String ID="NAVMENU_add_friend_request_only_Full" CONTENT="Add Friend"/>
	<String ID="NAVMENU_add_item_request_only_Short" CONTENT="Add item"/>
	<String ID="NAVMENU_add_item_request_only_Full" CONTENT="Add item"/>
	<String ID="NAVMENU_add_producerecipe_request_only_Short" CONTENT="Add crafting recipe"/>
	<String ID="NAVMENU_add_producerecipe_request_only_Full" CONTENT="Add crafting recipe"/>
	<String ID="NAVMENU_add_skill_request_only_Short" CONTENT="Add Skill"/>
	<String ID="NAVMENU_add_skill_request_only_Full" CONTENT="Add Skill"/>
	<String ID="NAVMENU_add_usercrest_request_only_Short" CONTENT="Add Crest"/>
	<String ID="NAVMENU_add_usercrest_request_only_Full" CONTENT="Add sentence"/>
	<String ID="NAVMENU_add_userrestriction_request_only_Short" CONTENT="Add Restriction"/>
	<String ID="NAVMENU_add_userrestriction_request_only_Full" CONTENT="Add Restriction"/>
	<String ID="NAVMENU_change_artisanProduction_request_only_Short" CONTENT="Change Production Proficiency"/>
	<String ID="NAVMENU_change_artisanProduction_request_only_Full" CONTENT="Change Production Proficiency"/>
	<String ID="NAVMENU_change_userProfs_request_only_Short" CONTENT="Change Proficiency"/>
	<String ID="NAVMENU_change_userProfs_request_only_Full" CONTENT="Change Proficiency"/>
	<String ID="NAVMENU_change_money_request_only_Short" CONTENT="Inventon Change"/>
	<String ID="NAVMENU_change_money_request_only_Full" CONTENT="Change Increment"/>
	<String ID="NAVMENU_change_waremoney_request_only_Short" CONTENT="Change warehouse value"/>
	<String ID="NAVMENU_change_waremoney_request_only_Full" CONTENT="Change warehouse value"/>
	<String ID="NAVMENU_del_achievement_request_only_Short" CONTENT="Delete Achievement"/>
	<String ID="NAVMENU_del_achievement_request_only_Full" CONTENT="Delete Achievement"/>
	<String ID="NAVMENU_del_artisanExtract_request_only_Short" CONTENT="Delete item extraction"/>
	<String ID="NAVMENU_del_artisanExtract_request_only_Full" CONTENT="Delete Extract"/>
	<String ID="NAVMENU_del_friend_request_only_Short" CONTENT="Delete Friend"/>
	<String ID="NAVMENU_del_friend_request_only_Full" CONTENT="Delete Friend"/>
	<String ID="NAVMENU_del_item_request_only_Short" CONTENT="Delete item"/>
	<String ID="NAVMENU_del_item_request_only_Full" CONTENT="Delete item"/>
	<String ID="NAVMENU_del_wareItem_request_only_Short" CONTENT="Delete Warehouse Item"/>
	<String ID="NAVMENU_del_wareItem_request_only_Full" CONTENT="Delete Warehouse Item"/>
	<String ID="NAVMENU_del_skill_request_only_Short" CONTENT="Delete Skill"/>
	<String ID="NAVMENU_del_skill_request_only_Full" CONTENT="Delete Skill"/>
	<String ID="NAVMENU_del_userCrest_request_only_Short" CONTENT="Delete Crest"/>
	<String ID="NAVMENU_del_userCrest_request_only_Full" CONTENT="Delete Crest"/>
	<String ID="NAVMENU_del_userRestriction_request_only_Short" CONTENT="Delete Sanction"/>
	<String ID="NAVMENU_del_userRestriction_request_only_Full" CONTENT="Delete User Restriction"/>
	<String ID="NAVMENU_undelete_user_request_only_Short" CONTENT="Restore User"/>
	<String ID="NAVMENU_undelete_user_request_only_Full" CONTENT="Undelete User"/>
	<String ID="NAVMENU_rename_user_request_only_Short" CONTENT="User Rename"/>
	<String ID="NAVMENU_rename_user_request_only_Full" CONTENT="User Rename"/>
	<String ID="NAVMENU_del_trade_broker_item_request_only_Short" CONTENT="Delete Auction"/>
	<String ID="NAVMENU_del_trade_broker_item_request_only_Full" CONTENT="Delete Auction Item"/>
	<String ID="NAVMENU_user_teleport_request_only_Short" CONTENT="Teleport"/>
	<String ID="NAVMENU_user_teleport_request_only_Full" CONTENT="Teleport"/>
	<String ID="NAVMENU_change_server_setting_request_only_Short" CONTENT="Change server settings"/>
	<String ID="NAVMENU_change_server_setting_request_only_Full" CONTENT="Change server settings"/>
	<String ID="NAVMENU_user_level_request_only_Short" CONTENT="Change Level"/>
	<String ID="NAVMENU_user_level_request_only_Full" CONTENT="Change User Level"/>
	<String ID="NAVMENU_user_exp_request_only_Short" CONTENT="Experience Change"/>
	<String ID="NAVMENU_user_exp_request_only_Full" CONTENT="Change character experience"/>
	<String ID="NAVMENU_change_guild_chief_request_only_Short" CONTENT="Change Guild Chapter"/>
	<String ID="NAVMENU_change_guild_chief_request_only_Full" CONTENT="Change Guild Chapter"/>
	<String ID="NAVMENU_delete_guild_request_only_Short" CONTENT="Delete Guild"/>
	<String ID="NAVMENU_delete_guild_request_only_Full" CONTENT="Delete Guild"/>
	<String ID="NAVMENU_change_guild_name_request_only_Short" CONTENT="Rename Guild"/>
	<String ID="NAVMENU_change_guild_name_request_only_Full" CONTENT="Rename Guild"/>
	<String ID="NAVMENU_change_guild_announce_request_only_Short" CONTENT="Change Guild Announcement"/>
	<String ID="NAVMENU_change_guild_announce_request_only_Full" CONTENT="Change Guild Announcement"/>
	<String ID="NAVMENU_change_guild_level_request_only_Short" CONTENT="Change Guild Level"/>
	<String ID="NAVMENU_change_guild_level_request_only_Full" CONTENT="Change Guild Level"/>
	<String ID="NAVMENU_change_guild_expire_request_only_Short" CONTENT="Change Guild Level Hold Time"/>
	<String ID="NAVMENU_change_guild_expire_request_only_Full" CONTENT="Change Guild Level Retention Time"/>
	<String ID="NAVMENU_change_crest_point_request_only_Short" CONTENT="Change Statement Point"/>
	<String ID="NAVMENU_change_crest_point_request_only_Full" CONTENT="Change statement point"/>
	<String ID="NAVMENU_delete_guild_logo_request_only_Short" CONTENT="Delete Guild Logo"/>
	<String ID="NAVMENU_delete_guild_logo_request_only_Full" CONTENT="Delete Guild Logo"/>
	<String ID="NAVMENU_change_guild_title_request_only_Short" CONTENT="Change Guild Title"/>
	<String ID="NAVMENU_change_guild_title_request_only_Full" CONTENT="Change Guild Title"/>
	<String ID="NAVMENU_seizure_item_request_only_Short" CONTENT="Seizure item"/>
	<String ID="NAVMENU_seizure_item_request_only_Full" CONTENT="Seizure item"/>
	<String ID="NAVMENU_returnseizure_item_request_only_Short" CONTENT="Return Seizure Item"/>
	<String ID="NAVMENU_returnseizure_item_request_only_Full" CONTENT="Return Seizure Item"/>
	<String ID="NAVMENU_add_quest_request_only_Short" CONTENT="Add Task"/>
	<String ID="NAVMENU_add_quest_request_only_Full" CONTENT="Add Task"/>
	<String ID="NAVMENU_change_quest_request_only_Short" CONTENT="Modify quest_request_only_Short"/>
	<String ID="NAVMENU_change_quest_request_only_Full" CONTENT="Quest Amendment"/>
	<String ID="NAVMENU_del_quest_request_only_Short" CONTENT="Clear Quasars"/>
	<String ID="NAVMENU_del_quest_request_only_Full" CONTENT="Clear Quasars"/>
	<String ID="NAVMENU_seizure_vendingmachine_request_only_Short" CONTENT="Seizure vending machine"/>
	<String ID="NAVMENU_seizure_vendingmachine_request_only_Full" CONTENT="Seizure vending machine"/>
	<String ID="NAVMENU_stop_store_request_only_Short" CONTENT="Store interrupted"/>
	<String ID="NAVMENU_stop_store_request_only_Full" CONTENT="Store interrupted"/>
	<String ID="NAVMENU_seizure_money_request_only_Short" CONTENT="Gold Seizure"/>
	<String ID="NAVMENU_seizure_money_request_only_Full" CONTENT="Gold Seizure"/>
	<String ID="NAVMENU_returnseizure_money_request_only_Short" CONTENT="Confiscation of Gold"/>
	<String ID="NAVMENU_returnseizure_money_request_only_Full" CONTENT="Confiscation of Gold"/>
	<String ID="NAVMENU_delete_any_memo_Short" CONTENT="Can delete all notes"/>
	<String ID="NAVMENU_delete_any_memo_Full" CONTENT="Can delete all memory"/>
	<String ID="NAVMENU_modify_any_memo_Short" CONTENT="All notes can be modified"/>
	<String ID="NAVMENU_modify_any_memo_Full" CONTENT="All memo can be modified"/>
	<String ID="NAVMENU_drtk_Short" CONTENT="Execute Action"/>
	<String ID="NAVMENU_drtk_Full" CONTENT="Execute Action"/>
	<String ID="NAVMENU_cancel_task_Short" CONTENT="Cancel operation"/>
	<String ID="NAVMENU_cancel_task_Full" CONTENT="Cancel operation"/>
	<String ID="NAVMENU_seizure_parcel_Short" CONTENT="Seizure Parcel"/>
	<String ID="NAVMENU_seizure_parcel_Full" CONTENT="Seizure Parcel"/>
	<String ID="NAVMENU_delete_seizure_item_Short" CONTENT="Delete Seizure Item"/>
	<String ID="NAVMENU_delete_seizure_item_Full" CONTENT="Delete Seizure Item"/>
	<String ID="NAVMENU_delete_seizure_money_Short" CONTENT="Delete Seizure Deposit"/>
	<String ID="NAVMENU_delete_seizure_money_Full" CONTENT="Delete Deduction Deposit"/>
	<String ID="NAVMENU_request_disable_territory_Short" CONTENT="Request Disable Territory"/>
	<String ID="NAVMENU_request_disable_territory_Full" CONTENT="Request Disable Territory"/>
	<String ID="NAVMENU_request_disable_territory_request_only_Short" CONTENT="Stop catalog gangway"/>
	<String ID="NAVMENU_request_disable_territory_request_only_Full" CONTENT="Disable Territory gangway"/>
	<String ID="NAVMENU_set_respawn_time_Short" CONTENT="Respawn Time"/>
	<String ID="NAVMENU_set_respawn_time_Full" CONTENT="Change "/>
	<String ID="NAVMENU_set_respawn_time_request_only_Short" CONTENT="Change Respawn Time"/>
	<String ID="NAVMENU_set_respawn_time_request_only_Full" CONTENT="Change Respawn Time"/>
	<String ID="NAVMENU_dungeon_npc_respawn_Short" CONTENT="Dungeon NPC Respawn"/>
	<String ID="NAVMENU_dungeon_npc_respawn_Full" CONTENT="Dungeon NPC Respawn"/>
	<String ID="NAVMENU_dungeon_npc_respawn_request_only_Short" CONTENT="Dungeon NPC Respawn Request"/>
	<String ID="NAVMENU_dungeon_npc_respawn_request_only_Full" CONTENT="Dungeon NPC Respawn Request"/>
	<String ID="NAVMENU_dungeon_cooltime_reset_Short" CONTENT="Reset Dungeon Cooltime"/>
	<String ID="NAVMENU_dungeon_cooltime_reset_Full" CONTENT="Reset Dungeon Cooltime"/>
	<String ID="NAVMENU_dungeon_cooltime_reset_request_only_Short" CONTENT="Reset Dungeon Cooltime"/>
	<String ID="NAVMENU_dungeon_cooltime_reset_request_only_Full" CONTENT="Reset Dungeon Cooltime"/>
	<String ID="NAVMENU_dungeon_enterCount_reset_Short" CONTENT="Reset Dungeon Entry Count"/>
	<String ID="NAVMENU_dungeon_enterCount_reset_Full" CONTENT="Reset Dungeon Entry Count"/>
	<String ID="NAVMENU_dungeon_enterCount_reset_request_only_Short" CONTENT="Reset Dungeon Entry Count"/>
	<String ID="NAVMENU_dungeon_enterCount_reset_request_only_Full" CONTENT="Reset dungeon entry count"/>
	<String ID="NAVMENU_enable_politics_Short" CONTENT="Enable Political System"/>
	<String ID="NAVMENU_enable_politics_Full" CONTENT="Enable Political System"/>
	<String ID="NAVMENU_enable_politics_request_only_Short" CONTENT="Enable Political System"/>
	<String ID="NAVMENU_enable_politics_request_only_Full" CONTENT="Enable Political System"/>
	<String ID="NAVMENU_disable_politics_Short" CONTENT="Close Political System"/>
	<String ID="NAVMENU_disable_politics_Full" CONTENT="Disable Political System"/>
	<String ID="NAVMENU_disable_politics_request_only_Short" CONTENT="Disable Political System"/>
	<String ID="NAVMENU_disable_politics_request_only_Full" CONTENT="Disable political system"/>
	<String ID="NAVMENU_clear_client_setting_Short" CONTENT="Remove ClientSetting"/>
	<String ID="NAVMENU_clear_client_setting_Full" CONTENT="Remove ClientSetting"/>
	<String ID="NAVMENU_clear_client_setting_request_only_Short" CONTENT="Remove ClientSetting"/>
	<String ID="NAVMENU_clear_client_setting_request_only_Full" CONTENT="Remove ClientSetting"/>
	<String ID="NAVMENU_change_guard_notice_Short" CONTENT="Change Guard Notice"/>
	<String ID="NAVMENU_change_guard_notice_Full" CONTENT="Change guard notification"/>
	<String ID="NAVMENU_change_guard_notice_request_only_Short" CONTENT="Change guard notification"/>
	<String ID="NAVMENU_change_guard_notice_request_only_Full" CONTENT="Change guard notification"/>
	<String ID="NAVMENU_change_guard_policy_point_Short" CONTENT="Change reserved policy point"/>
	<String ID="NAVMENU_change_guard_policy_point_Full" CONTENT="Change reserved policy point"/>
	<String ID="NAVMENU_change_guard_policy_point_request_only_Short" CONTENT="Change reserved policy point"/>
	<String ID="NAVMENU_change_guard_policy_point_request_only_Full" CONTENT="Change reserved policy point"/>
	<String ID="NAVMENU_change_guard_tax_rate_Short" CONTENT="Change guard rate"/>
	<String ID="NAVMENU_change_guard_tax_rate_Full" CONTENT="Change guard rate"/>
	<String ID="NAVMENU_change_guard_tax_rate_request_only_Short" CONTENT="Change guard rate"/>
	<String ID="NAVMENU_change_guard_tax_rate_request_only_Full" CONTENT="Change guard rate"/>
	<String ID="NAVMENU_change_guard_policy_Short" CONTENT="Change guard policy"/>
	<String ID="NAVMENU_change_guard_policy_Full" CONTENT="Change guard policy"/>
	<String ID="NAVMENU_change_guard_policy_request_only_Short" CONTENT="Change guard policy"/>
	<String ID="NAVMENU_change_guard_policy_request_only_Full" CONTENT="Change guard policy"/>
	<String ID="NAVMENU_change_lord_competition_point_Short" CONTENT="Change Permanent Resident Candidate Competition Point"/>
	<String ID="NAVMENU_change_lord_competition_point_Full" CONTENT="Change Permanent Resident Candidate Competition Point"/>
	<String ID="NAVMENU_change_lord_competition_point_request_only_Short" CONTENT="Change permanent resident candidate competition score"/>
	<String ID="NAVMENU_change_lord_competition_point_request_only_Full" CONTENT="Change permanent resident candidate competition score"/>
	<String ID="NAVMENU_dismiss_lord_Short" CONTENT="Forced Dismissal of Permanent Residence"/>
	<String ID="NAVMENU_dismiss_lord_Full" CONTENT="Force Dismissal of Permanent Residence"/>
	<String ID="NAVMENU_dismiss_lord_request_only_Short" CONTENT="Forced Dismissal of Permanent Residence"/>
	<String ID="NAVMENU_dismiss_lord_request_only_Full" CONTENT="Forced Dismissal of Permanent Residence"/>
	<String ID="NAVMENU_change_guard_tax_Short" CONTENT="Change guard tax"/>
	<String ID="NAVMENU_change_guard_tax_Full" CONTENT="Change guard tax"/>
	<String ID="NAVMENU_change_guard_tax_request_only_Short" CONTENT="Change guard tax"/>
	<String ID="NAVMENU_change_guard_tax_request_only_Full" CONTENT="Change guard tax"/>
	<String ID="NAVMENU_reset_vote_Short" CONTENT="Remove Voting History"/>
	<String ID="NAVMENU_reset_vote_Full" CONTENT="Remove Voting History"/>
	<String ID="NAVMENU_reset_vote_request_only_Short" CONTENT="Remove Voting History"/>
	<String ID="NAVMENU_reset_vote_request_only_Full" CONTENT="Remove Voting History"/>
	<String ID="NAVMENU_reset_exterior_Short" CONTENT="Repair deformation"/>
	<String ID="NAVMENU_reset_exterior_Full" CONTENT="Repair deformation"/>
	<String ID="NAVMENU_reset_exterior_request_only_Short" CONTENT="Repair Deformation"/>
	<String ID="NAVMENU_reset_exterior_request_only_Full" CONTENT="Repair deformation"/>
	<String ID="NAVMENU_reset_coloring_Short" CONTENT="Reset Coloring"/>
	<String ID="NAVMENU_reset_coloring_Full" CONTENT="Reset Coloring"/>
	<String ID="NAVMENU_reset_coloring_request_only_Short" CONTENT="Reset Coloring"/>
	<String ID="NAVMENU_reset_coloring_request_only_Full" CONTENT="Repair Coloring"/>
	<String ID="NAVMENU_change_lord_competition_pledge_Short" CONTENT="Change Permanent Resident Candidate Pledge"/>
	<String ID="NAVMENU_change_lord_competition_pledge_Full" CONTENT="Change Permanent Resident Candidate Pledge"/>
	<String ID="NAVMENU_change_lord_competition_pledge_request_only_Short" CONTENT="Change Permanent Resident Candidate Commitment"/>
	<String ID="NAVMENU_change_lord_competition_pledge_request_only_Full" CONTENT="Change Permanent Resident Candidate Pledge"/>
	<String ID="NAVMENU_toggle_artisan_bookmark_Short" CONTENT="Change recipe bookmark"/>
	<String ID="NAVMENU_toggle_artisan_bookmark_Full" CONTENT="Change recipe bookmark"/>
	<String ID="NAVMENU_toggle_artisan_bookmark_request_only_Short" CONTENT="Change recipe bookmark"/>
	<String ID="NAVMENU_toggle_artisan_bookmark_request_only_Full" CONTENT="Change recipe bookmark"/>
	<String ID="NAVMENU_set_world_festival_Short" CONTENT="Set World Festival"/>
	<String ID="NAVMENU_set_world_festival_Full" CONTENT="Set World Festival"/>
	<String ID="NAVMENU_set_world_festival_request_only_Short" CONTENT="World Event Action Settings"/>
	<String ID="NAVMENU_set_world_festival_request_only_Full" CONTENT="World Event Action Settings"/>
	<String ID="NAVMENU_set_world_festival_param_Short" CONTENT="Set concurrent world event max object"/>
	<String ID="NAVMENU_set_world_festival_param_Full" CONTENT="Set concurrent world event maximum object"/>
	<String ID="NAVMENU_set_world_festival_param_request_only_Short" CONTENT="Set concurrent world event max object"/>
	<String ID="NAVMENU_set_world_festival_param_request_only_Full" CONTENT="Set concurrent world event maximum object"/>
	<String ID="NAVMENU_seizure_trade_broker_item_Short" CONTENT="Seizure Trade Broker Registration Item"/>
	<String ID="NAVMENU_seizure_trade_broker_item_Full" CONTENT="Seizure Trading Broker Registration Item"/>
	<String ID="NAVMENU_Server_Short" CONTENT="Server Management"/>
	<String ID="NAVMENU_Server_Full" CONTENT="Server Management"/>
	<String ID="NAVMENU_ServerMonitor_Short" CONTENT="Running Status"/>
	<String ID="NAVMENU_ServerMonitor_Full" CONTENT="Running Status"/>
	<String ID="NAVMENU_ServerView_Short" CONTENT="Server View"/>
	<String ID="NAVMENU_ServerView_Full" CONTENT="Server View"/>
	<String ID="NAVMENU_Announce_Short" CONTENT="Announcement Management"/>
	<String ID="NAVMENU_Announce_Full" CONTENT="Announcement Management"/>
	<String ID="NAVMENU_AnnounceList_Short" CONTENT="Game Announcement"/>
	<String ID="NAVMENU_AnnounceList_Full" CONTENT="Game Announcement"/>
	<String ID="NAVMENU_Account_Short" CONTENT="Account Management"/>
	<String ID="NAVMENU_Account_Full" CONTENT="Account Management Menu"/>
	<String ID="NAVMENU_SearchAllServers_Short" CONTENT="Retrieve Accounts"/>
	<String ID="NAVMENU_SearchAllServers_Full" CONTENT="Search Accounts"/>
	<String ID="NAVMENU_AccountMemo_Short" CONTENT="Manage Log"/>
	<String ID="NAVMENU_AccountMemo_Full" CONTENT="Manage Logs"/>
	<String ID="NAVMENU_SearchChangeName_Short" CONTENT="Search Name Change"/>
	<String ID="NAVMENU_SearchChangeName_Full" CONTENT="Query User Rename Information"/>
	<String ID="NAVMENU_SearchChangeAppearance_Short" CONTENT="Search Appearance Change "/>
	<String ID="NAVMENU_SearchChangeAppearance_Full" CONTENT="Search Race, Gender and Appearance Change"/>
	<String ID="NAVMENU_SearchChangeServer_Short" CONTENT="Search Server Change"/>
	<String ID="NAVMENU_SearchChangeServer_Full" CONTENT="Search User Transfer Servers"/>
	<String ID="NAVMENU_SearchMergeServer_Short" CONTENT="Search Server Merge"/>
	<String ID="NAVMENU_SearchMergeServer_Full" CONTENT="Search server information where the user is located"/>
	<String ID="NAVMENU_SearchAllClosedServers_Short" CONTENT="Search All Closed Servers"/>
	<String ID="NAVMENU_SearchAllClosedServers_Full" CONTENT="Search all service area user information"/>
	<String ID="NAVMENU_ChangeServer_Short" CONTENT="User Transfer"/>
	<String ID="NAVMENU_ChangeServer_Full" CONTENT="User Transfer"/>
	<String ID="NAVMENU_ServerSetting_Short" CONTENT="Server Setting"/>
	<String ID="NAVMENU_ServerSetting_Full" CONTENT="Server Setting"/>
	<String ID="NAVMENU_Users_Short" CONTENT="User Management"/>
	<String ID="NAVMENU_Users_Full" CONTENT="User Management Menu"/>
	<String ID="NAVMENU_SearchUser_Short" CONTENT="Search User"/>
	<String ID="NAVMENU_SearchUser_Full" CONTENT="Search User Details"/>
	<String ID="NAVMENU_ItemInfo_Short" CONTENT="User Item Info"/>
	<String ID="NAVMENU_ItemInfo_Full" CONTENT="Item Info"/>
	<String ID="NAVMENU_SeizureItem_Short" CONTENT="Seizure Item"/>
	<String ID="NAVMENU_SeizureItem_Full" CONTENT="View Seizure Item"/>
	<String ID="NAVMENU_WareHouse_Short" CONTENT="Warehouse"/>
	<String ID="NAVMENU_UserWareHouse_Short" CONTENT="User Warehouse"/>
	<String ID="NAVMENU_WareHouse_Full" CONTENT="View User information"/>
	<String ID="NAVMENU_QuestInfo_Short" CONTENT="Quest"/>
	<String ID="NAVMENU_QuestInfo_Full" CONTENT="View User Quest"/>
	<String ID="NAVMENU_SkillInfo_Short" CONTENT="Skill"/>
	<String ID="NAVMENU_SkillInfo_Full" CONTENT="View User Skills"/>
	<String ID="NAVMENU_Style_Short" CONTENT="Style Equipped"/>
	<String ID="NAVMENU_Style_Full" CONTENT="View Style Info"/>
	<String ID="NAVMENU_ParcelInfo_Short" CONTENT="Mail"/>
	<String ID="NAVMENU_ParcelInfo_Full" CONTENT="View Mail Info"/>
	<String ID="NAVMENU_TradeBrokerInfo_Short" CONTENT="Trade Broker"/>
	<String ID="NAVMENU_TradeBrokerInfo_Full" CONTENT="Trade Broker"/>
	<String ID="NAVMENU_PetInfo_Short" CONTENT="Pet Info"/>
	<String ID="NAVMENU_PetInfo_Full" CONTENT="View Pet Info"/>
	<String ID="NAVMENU_GuildInfo_Short" CONTENT="Guild"/>
	<String ID="NAVMENU_GuildInfo_Full" CONTENT="View Guild Info"/>
	<String ID="NAVMENU_Artisan_Short" CONTENT="Crafting Recipe"/>
	<String ID="NAVMENU_Artisan_Full" CONTENT="View Manufacturing Information"/>
	<String ID="NAVMENU_Prof_Short" CONTENT="Crafting Proficiency"/>
	<String ID="NAVMENU_Prof_Full" CONTENT="View Proficiency Information"/>
	<String ID="NAVMENU_UserHistory_Short" CONTENT="PVP Information"/>
	<String ID="NAVMENU_UserHistory_Full" CONTENT="View PvP PK History"/>
	<String ID="NAVMENU_UserMemo_Short" CONTENT="Log"/>
	<String ID="NAVMENU_UserMemo_Full" CONTENT="View notes (such as account notes or exclusions)"/>
	<String ID="NAVMENU_Crest_Short" CONTENT="Crest"/>
	<String ID="NAVMENU_Crest_Full" CONTENT="View Statement"/>
	<String ID="NAVMENU_Achievement_Short" CONTENT="Achievement"/>
	<String ID="NAVMENU_Achievement_Full" CONTENT="View Achievements"/>
	<String ID="NAVMENU_Friend_Short" CONTENT="Friend"/>
	<String ID="NAVMENU_Friend_Full" CONTENT="View Friends List"/>
	<String ID="NAVMENU_UserRestrict_Short" CONTENT="User Restrictions"/>
	<String ID="NAVMENU_UserRestrict_Full" CONTENT="View User Restrictions"/>
	<String ID="NAVMENU_UserVote_Short" CONTENT="Vote"/>
	<String ID="NAVMENU_UserVote_Full" CONTENT="View Vote"/>
	<String ID="NAVMENU_CreateHistory_Short" CONTENT="Search User Basic Information"/>
	<String ID="NAVMENU_CreateHistory_Full" CONTENT="Search User Basic Information"/>
	<String ID="NAVMENU_GMInfo_Short" CONTENT="GM Info Settings"/>
	<String ID="NAVMENU_GMInfo_Full" CONTENT="GM Info"/>
	<String ID="NAVMENU_CustomBookmark_Short" CONTENT="GM Manage Bookmarks"/>
	<String ID="NAVMENU_CustomBookmark_Full" CONTENT="GM Manage Bookmarks"/>
	<String ID="NAVMENU_ClearClientSetting_Short" CONTENT="Clear User Client Setting"/>
	<String ID="NAVMENU_ClearClientSetting_Full" CONTENT="Clear User Client Setting"/>
	<String ID="NAVMENU_Npc_Short" CONTENT="Npc Management"/>
	<String ID="NAVMENU_Npc_Full" CONTENT="Npc Management Menu"/>
	<String ID="NAVMENU_DisableTerritory_Short" CONTENT="Disable Territory"/>
	<String ID="NAVMENU_DisableTerritory_Full" CONTENT="Disable Territory"/>
	<String ID="NAVMENU_DisabledTerritoryList_Short" CONTENT="Disabled Territory List"/>
	<String ID="NAVMENU_DisabledTerritoryList_Full" CONTENT="Disabled Territory List"/>
	<String ID="NAVMENU_SetRespawnTime_Short" CONTENT="Set Respawn Time"/>
	<String ID="NAVMENU_SetRespawnTime_Full" CONTENT="Set Respawn Time"/>
	<String ID="NAVMENU_FieldNpcRespawn_Short" CONTENT="Field Npc Respawn"/>
	<String ID="NAVMENU_FieldNpcRespawn_Full" CONTENT="Field Npc Respawn"/>
	<String ID="NAVMENU_DungeonNpcRespawn_Short" CONTENT="Dungeon Npc Respawn"/>
	<String ID="NAVMENU_DungeonNpcRespawn_Full" CONTENT="Dungeon Npc Respawn"/>
	<String ID="NAVMENU_DungeonCooltimeReset_Short" CONTENT="Dungeon Cooltime Reset"/>
	<String ID="NAVMENU_DungeonCooltimeReset_Full" CONTENT="Dungeon Cooltime Reset"/>
	<String ID="NAVMENU_DungeonEnterCountReset_Short" CONTENT="Reset Dungeon Entry Count"/>
	<String ID="NAVMENU_DungeonEnterCountReset_Full" CONTENT="Reset Dungeon Entry Count"/>
	<String ID="NAVMENU_Restrict_Short" CONTENT="Restrictions Management"/>
	<String ID="NAVMENU_Restrict_Full" CONTENT="Restrictions Management"/>
	<String ID="NAVMENU_CharRestrict_Short" CONTENT="Characters Restriction"/>
	<String ID="NAVMENU_CharRestrict_Full" CONTENT="Characters Restriction"/>
	<String ID="NAVMENU_UserNote_Short" CONTENT="Management Information"/>
	<String ID="NAVMENU_UserNote_Full" CONTENT="Management Information"/>
	<String ID="NAVMENU_UserNoteList_Short" CONTENT="Account Notes"/>
	<String ID="NAVMENU_UserNoteList_Full" CONTENT="Account Notes"/>
	<String ID="NAVMENU_UserNoteDetail_Short" CONTENT="Account Note Details"/>
	<String ID="NAVMENU_UserNoteDetail_Full" CONTENT="Account Notes"/>
	<String ID="NAVMENU_Item_Short" CONTENT="Item Management"/>
	<String ID="NAVMENU_Item_Full" CONTENT="Item Management Search"/>
	<String ID="NAVMENU_SearchItemID_Short" CONTENT="SearchItemID"/>
	<String ID="NAVMENU_SearchItemID_Full" CONTENT="Search Item Template"/>
	<String ID="NAVMENU_SearchItemDBId_Short" CONTENT="Search ItemDBId"/>
	<String ID="NAVMENU_SearchItemDBId_Full" CONTENT="Search ItemDBID"/>
	<String ID="NAVMENU_SearchDeletedItem_Short" CONTENT="Search Deleted Item"/>
	<String ID="NAVMENU_SearchDeletedItem_Full" CONTENT="Search Deleted Item"/>
	<String ID="NAVMENU_SearchCashItemLog_Short" CONTENT="Cash Item Log"/>
	<String ID="NAVMENU_SearchCashItemLog_Full" CONTENT="Cash Item Log"/>
	<String ID="NAVMENU_Guild_Short" CONTENT="Guild Management"/>
	<String ID="NAVMENU_Guild_Full" CONTENT="Guild Management"/>
	<String ID="NAVMENU_SearchGuild_Short" CONTENT="Guild Search"/>
	<String ID="NAVMENU_SearchGuild_Full" CONTENT="Guild Search"/>
	<String ID="NAVMENU_Politics_Short" CONTENT="Manage Politics"/>
	<String ID="NAVMENU_Politics_Full" CONTENT="Manage Politics"/>
	<String ID="NAVMENU_PoliticsSystemOnOff_Short" CONTENT="Politics System On/Off"/>
	<String ID="NAVMENU_PoliticsSystemOnOff_Full" CONTENT="Politics System On/Off"/>
	<String ID="NAVMENU_PoliticsStateInfo_Short" CONTENT="Politics System Information"/>
	<String ID="NAVMENU_PoliticsStateInfo_Full" CONTENT="Politics System Information"/>
	<String ID="NAVMENU_PoliticsReignInfo_Short" CONTENT="Politics Reign Info"/>
	<String ID="NAVMENU_PoliticsReignInfo_Full" CONTENT="Politics Reign Info"/>
	<String ID="NAVMENU_PoliticsCompetitionInfo_Short" CONTENT="Politics Competition Info"/>
	<String ID="NAVMENU_PoliticsCompetitionInfo_Full" CONTENT="Politics Competition Info"/>
	<String ID="NAVMENU_Quest_Short" CONTENT="Quest Management"/>
	<String ID="NAVMENU_Quest_Full" CONTENT="Quest Management"/>
	<String ID="NAVMENU_SearchQuest_Short" CONTENT="Quest Search"/>
	<String ID="NAVMENU_SearchQuest_Full" CONTENT="Quest Search"/>
	<String ID="NAVMENU_DetailQuestInfo_Short" CONTENT="Quest Info"/>
	<String ID="NAVMENU_DetailQuestInfo_Full" CONTENT="Quest Details"/>
	<String ID="NAVMENU_BattleField_Short" CONTENT="Battlefield Management"/>
	<String ID="NAVMENU_BattleField_Full" CONTENT="Battlefield Management"/>
	<String ID="NAVMENU_SearchBattleField_Short" CONTENT="Battlefield Search"/>
	<String ID="NAVMENU_SearchBattleField_Full" CONTENT="Battlefield Search"/>
	<String ID="NAVMENU_BattleFieldRanking_Short" CONTENT="BattleField Ranking"/>
	<String ID="NAVMENU_BattleFieldRanking_Full" CONTENT="BattleField Ranking Information"/>
	<String ID="NAVMENU_BattleFieldSetting_Short" CONTENT="BattleField Setting"/>
	<String ID="NAVMENU_BattleFieldSetting_Full" CONTENT="BattleField Setting"/>
	<String ID="NAVMENU_WorldFestival_Short" CONTENT="World Events"/>
	<String ID="NAVMENU_WorldFestival_Full" CONTENT="World Events"/>
	<String ID="NAVMENU_WorldFestivalOnOff_Short" CONTENT="World Events On/Off"/>
	<String ID="NAVMENU_WorldFestivalOnOff_Full" CONTENT="World Events On/Off"/>
	<String ID="NAVMENU_WorldFestivalScheduling_Short" CONTENT="World Events Scheduling"/>
	<String ID="NAVMENU_WorldFestivalScheduling_Full" CONTENT="World Events Scheduling"/>
	<String ID="NAVMENU_Task_Short" CONTENT="Operation Log"/>
	<String ID="NAVMENU_Task_Full" CONTENT="Operation Team Operating System"/>
	<String ID="NAVMENU_ViewTaskList_Short" CONTENT="ViewTaskList"/>
	<String ID="NAVMENU_ViewTaskList_Full" CONTENT="ViewTaskList"/>
	<String ID="NAVMENU_ViewTask_Short" CONTENT="ViewTask"/>
	<String ID="NAVMENU_ViewTask_Full" CONTENT="View Task Content"/>
	<String ID="NAVMENU_Log_Short" CONTENT="Player Log"/>
	<String ID="NAVMENU_Log_Full" CONTENT="Player Log"/>
	<String ID="NAVMENU_LogInfo_Short" CONTENT="Log List"/>
	<String ID="NAVMENU_LogInfo_Full" CONTENT="Log Details"/>
	<String ID="NAVMENU_SearchLog_Short" CONTENT="Search Log"/>
	<String ID="NAVMENU_SearchLog_Full" CONTENT="Search Log"/>
	<String ID="NAVMENU_LogAccount_Short" CONTENT="Member Log"/>
	<String ID="NAVMENU_LogAccount_Full" CONTENT="Member Log"/>
	<String ID="NAVMENU_SearchByIP_Short" CONTENT="SearchByIP"/>
	<String ID="NAVMENU_SearchByIP_Full" CONTENT="Search by IP to log in"/>
	<String ID="NAVMENU_SearchByLogAction_Short" CONTENT="Search by Log Action"/>
	<String ID="NAVMENU_SearchByLogAction_Full" CONTENT="Search by Log Action"/>
	<String ID="NAVMENU_SearchFromAuditLog_Short" CONTENT="View Audit Log"/>
	<String ID="NAVMENU_SearchFromAuditLog_Full" CONTENT="Log Audit Log Query"/>
	<String ID="NAVMENU_SearchChat_Short" CONTENT="Chat Search"/>
	<String ID="NAVMENU_SearchChat_Full" CONTENT="Chat Search"/>
	<String ID="NAVMENU_Report_Short" CONTENT="Report"/>
	<String ID="NAVMENU_Report_Full" CONTENT="View Report"/>
	<String ID="NAVMENU_FirstReport_Short" CONTENT="Default Report"/>
	<String ID="NAVMENU_FirstReport_Full" CONTENT="Basic Report"/>
	<String ID="NAVMENU_KillNPCCountClassLevel_Short" CONTENT="Number of NPC hunts"/>
	<String ID="NAVMENU_KillNPCCountClassLevel_Full" CONTENT="How many NPCs were killed per class level"/>
	<String ID="NAVMENU_KillNPCCountLevel_Short" CONTENT="Number of NPC hunts"/>
	<String ID="NAVMENU_KillNPCCountLevel_Full" CONTENT="How many NPCs were killed per level"/>
	<String ID="NAVMENU_KillNPCEarnExpClassLevel_Short" CONTENT="NPC hunting experience"/>
	<String ID="NAVMENU_KillNPCEarnExpClassLevel_Full" CONTENT="How much NPC hunting experience for each class level"/>
	<String ID="NAVMENU_KillNPCEarnExp_Short" CONTENT="NPC hunting experience"/>
	<String ID="NAVMENU_KillNPCEarnExp_Full" CONTENT="How much NPC hunting experience is gained per level"/>
	<String ID="NAVMENU_CastSkill_Short" CONTENT="CastSkill"/>
	<String ID="NAVMENU_CastSkill_Full" CONTENT="How much is used for what skill"/>
	<String ID="NAVMENU_GetItem_Short" CONTENT="Get Item"/>
	<String ID="NAVMENU_GetItem_Full" CONTENT="What item was obtained"/>
	<String ID="NAVMENU_LevelUpClassLevel_Short" CONTENT="LevelUpClassLevel"/>
	<String ID="NAVMENU_LevelUpClassLevel_Full" CONTENT="Class specific level up time"/>
	<String ID="NAVMENU_LevelUpLevel_Short" CONTENT="Upgrade character level"/>
	<String ID="NAVMENU_LevelUpLevel_Full" CONTENT="Character upgrade time"/>
	<String ID="NAVMENU_NpcDead_Short" CONTENT="Number of NPC deaths"/>
	<String ID="NAVMENU_NpcDead_Full" CONTENT="Number of NPC deaths"/>
	<String ID="NAVMENU_NpcDropItem_Short" CONTENT="Item dropped by NPC"/>
	<String ID="NAVMENU_NpcDropItem_Full" CONTENT="Item dropped by NPC"/>
	<String ID="NAVMENU_PcDeadByNPCClassLevel_Short" CONTENT="Number of kills by NPC"/>
	<String ID="NAVMENU_PcDeadByNPCClassLevel_Full" CONTENT="Class level character killed by NPC"/>
	<String ID="NAVMENU_PcDeadByNPCLevel_Short" CONTENT="Number of kills by NPC"/>
	<String ID="NAVMENU_PcDeadByNPCLevel_Full" CONTENT="Characters killed by NPC by level"/>
	<String ID="NAVMENU_PcDeadByPcClassLevel_Short" CONTENT="Number of kills by PC"/>
	<String ID="NAVMENU_PcDeadByPcClassLevel_Full" CONTENT="Class-level character killed by PC"/>
	<String ID="NAVMENU_PcDeadByPcLevel_Short" CONTENT="Number of kills by PC"/>
	<String ID="NAVMENU_PcDeadByPcLevel_Full" CONTENT="Characters who die on PC by level"/>
	<String ID="NAVMENU_UseItem_Short" CONTENT="Use Item"/>
	<String ID="NAVMENU_UseItem_Full" CONTENT="Item used"/>
	<String ID="NAVMENU_CurrentCharTop_Short" CONTENT="Location of the largest concentration of characters"/>
	<String ID="NAVMENU_CurrentCharTop_Full" CONTENT="100 places where characters are usually found"/>
	<String ID="NAVMENU_CurrentChar_Short" CONTENT="Where there are many characters"/>
	<String ID="NAVMENU_CurrentChar_Full" CONTENT="The place where the characters usually are"/>
	<String ID="NAVMENU_CurrentWeaponItem_Short" CONTENT="Install Weapon Item"/>
	<String ID="NAVMENU_CurrentWeaponItem_Full" CONTENT="Main equipment weapon item"/>
	<String ID="NAVMENU_CurrentBodyItem_Short" CONTENT="Install Protector"/>
	<String ID="NAVMENU_CurrentBodyItem_Full" CONTENT="Main equipment protective items"/>
	<String ID="NAVMENU_HuntingNpcLoc_Short" CONTENT="Where NPCs hunt"/>
	<String ID="NAVMENU_HuntingNpcLoc_Full" CONTENT="Where do NPCs hunt more"/>
	<String ID="NAVMENU_HuntingNpcLocTop_Short" CONTENT="Where to mainly hunt NPCs"/>
	<String ID="NAVMENU_HuntingNpcLocTop_Full" CONTENT="Top 100 NPCs to hunt"/>
	<String ID="NAVMENU_EarnExpLoc_Short" CONTENT="Where to get experience"/>
	<String ID="NAVMENU_EarnExpLoc_Full" CONTENT="Where to get more experience"/>
	<String ID="NAVMENU_EarnExpLocTop_Short" CONTENT="Where to get experience from"/>
	<String ID="NAVMENU_EarnExpLocTop_Full" CONTENT="Top 100 places to experience"/>
	<String ID="NAVMENU_GetMoney_Short" CONTENT="Get Money"/>
	<String ID="NAVMENU_GetMoney_Full" CONTENT="Character with the most money"/>
	<String ID="NAVMENU_DelMoney_Short" CONTENT="Money disappeared"/>
	<String ID="NAVMENU_DelMoney_Full" CONTENT="Character who disappeared the most"/>
	<String ID="NAVMENU_DeltaMoney_Short" CONTENT="Money Change"/>
	<String ID="NAVMENU_DeltaMoney_Full" CONTENT="Character with the most money change"/>
	<String ID="NAVMENU_DelItemPrice_Short" CONTENT="Disappearing item value"/>
	<String ID="NAVMENU_DelItemPrice_Full" CONTENT="Lost Item Value Character"/>
	<String ID="NAVMENU_GetItemPrice_Short" CONTENT="Get item value"/>
	<String ID="NAVMENU_GetItemPrice_Full" CONTENT="Character with great value of props obtained"/>
	<String ID="NAVMENU_DeltaItemPrice_Short" CONTENT="Item Value Change"/>
	<String ID="NAVMENU_DeltaItemPrice_Full" CONTENT="Characters with large fluctuations in item value"/>
	<String ID="NAVMENU_Connections_Short" CONTENT="Synonyms Report"/>
	<String ID="NAVMENU_Connections_Full" CONTENT="View concurrent connections"/>
	<String ID="NAVMENU_CharCreateByClass_Short" CONTENT="Create Character (Class)"/>
	<String ID="NAVMENU_CharCreateByClass_Full" CONTENT="Number of times to create characters by class"/>
	<String ID="NAVMENU_CharCreateByRace_Short" CONTENT="Create Character (Race)"/>
	<String ID="NAVMENU_CharCreateByRace_Full" CONTENT="Character creation times per race"/>
	<String ID="NAVMENU_CharDeleteByClass_Short" CONTENT="Delete Character (Class)"/>
	<String ID="NAVMENU_CharDeleteByClass_Full" CONTENT="Number of times to delete characters by class"/>
	<String ID="NAVMENU_CharDeleteByRace_Short" CONTENT="Delete Character (Race)"/>
	<String ID="NAVMENU_CharDeleteByRace_Full" CONTENT="CharDelete By Race"/>
	<String ID="NAVMENU_UserEventMatching_Short" CONTENT="Daily Event"/>
	<String ID="NAVMENU_UserEventMatching_Full" CONTENT="User's daily game activity progress information"/>
	<String ID="NAVMENU_GuildCompStatus_Short" CONTENT="Guild Competition Status"/>
	<String ID="NAVMENU_GuildCompStatus_Full" CONTENT="Request Guild Competition Status"/>
	<String ID="NAVMENU_GuildCompManagement_Short" CONTENT="Control of Guild Competitions"/>
	<String ID="NAVMENU_GuildCompManagement_Full" CONTENT="Should Guild Competition Content be Used"/>
	<String ID="NAVMENU_GuildCompInfo_Short" CONTENT="Guild Competition"/>
	<String ID="NAVMENU_GuildCompInfo_Full" CONTENT="Guild Competition Content Rating"/>
	<String ID="NAVMENU_Statistics_Short" CONTENT="Statistics"/>
	<String ID="NAVMENU_Statistics_Full" CONTENT="Statistics"/>
	<String ID="NAVMENU_ItemStatistics_Short" CONTENT="Item Statistics"/>
	<String ID="NAVMENU_ItemStatistics_Full" CONTENT="Total item statistics in server"/>
	<String ID="QuestRepeat" CONTENT="QuestRepeat"/>
	<String ID="QuestSingle" CONTENT="QuestSingle"/>
	<String ID="QuestTypeVisit" CONTENT="QuestTypeVisit"/>
	<String ID="QuestTypeGroupHunting" CONTENT="QuestTypeGroupHunting"/>
	<String ID="QuestTypeHunting" CONTENT="QuestTypeHunting"/>
	<String ID="QuestTypeDelivery" CONTENT="QuestTypeDelivery"/>
	<String ID="QuestTypeBranch" CONTENT="QuestTypeBranch"/>
	<String ID="QuestTypeHuntingDelivery" CONTENT="QuestTypeHuntingDelivery"/>
	<String ID="QuestTypeLoop" CONTENT="QuestTypeLoop"/>
	<String ID="QuestTypePcMove" CONTENT="QuestTypePcMove"/>
	<String ID="QuestTypeTemper" CONTENT="QuestTypeTemper"/>
	<String ID="QuestTypeDeath" CONTENT="QuestTypeDeath"/>
	<String ID="QuestTypeItemDelivery" CONTENT="QuestTypeItemDelivery"/>
	<String ID="QuestTypeSneak" CONTENT="QuestTypeSneak"/>
	<String ID="QuestTypeProtect" CONTENT="GuardianTask"/>
	<String ID="QuestTypeGuard" CONTENT="QuestTypeGuard"/>
	<String ID="QuestTypeCollecting" CONTENT="QuestTypeCollecting"/>
	<String ID="QuestTypeCondition" CONTENT="QuestTypeCondition"/>
	<String ID="QuestTypeMovie" CONTENT="QuestTypeMovie"/>
	<String ID="QuestTypeItemCollecting" CONTENT="Hunting Collecting Quest"/>
	<String ID="QuestTypeTransform" CONTENT="Transform Task"/>
	<String ID="QuestTypeWorkObject" CONTENT="QuestTypeWorkObject"/>
	<String ID="QuestTypeAbnormality" CONTENT="QuestTypeAbnormality"/>
	<String ID="LT_USER_WAREHOUSE_MONEY_PUT" CONTENT="Put money into warehouse"/>
	<String ID="LT_USER_WAREHOUSE_MONEY_GET" CONTENT="Find money in the warehouse"/>
	<String ID="GuildWareSeizureMoneyTitle" CONTENT="Guild Warehouse Deposit Coins"/>
	<String ID="AdminRewardId" CONTENT="AdminRewardId"/>
	<String ID="AdminRewardDesc" CONTENT="Description"/>
	<String ID="AdminRewardBeginTime" CONTENT="Start Time"/>
	<String ID="AdminRewardEndTime" CONTENT="End Time"/>
	<String ID="EXECUTE_ADMIN_REWARD" CONTENT="Run Admin Reward"/>
	<String ID="ConfirmExecuteAdminReward" CONTENT="View Admin Reward Instructions to Run"/>
	<String ID="NAVMENU_execute_admin_reward_Short" CONTENT="Run Admin Reward"/>
	<String ID="NAVMENU_execute_admin_reward_Full" CONTENT="Run Admin Reward"/>
	<String ID="NAVMENU_AdminReward_Short" CONTENT="Protection Reward"/>
	<String ID="NAVMENU_AdminReward_Full" CONTENT="AdminReward_Full"/>
	<String ID="NAVMENU_ExecuteAdminReward_Short" CONTENT="Execute Reward"/>
	<String ID="NAVMENU_ExecuteAdminReward_Full" CONTENT="Execute Reward"/>
	<String ID="LOGINFO_UserPartMemberCount" CONTENT="User Party Member Number"/>
	<String ID="LOGINFO_UserBattleSec" CONTENT="Battle time after login (seconds)"/>
	<String ID="LOGINFO_UserPartySec" CONTENT="Party time after login (seconds)"/>
	<String ID="LOGINFO_UserLevelUpDesc" CONTENT="Log left by user upgrade"/>
	<String ID="LOGINFO_UserPlaySecSinceLevelUp" CONTENT="Level up to now (seconds)"/>
	<String ID="LOGINFO_UserBattleSecSinceLivelUp" CONTENT="Battle time (seconds) from level upgrade to now"/>
	<String ID="LOGINFO_UserPartySecSinceLivelUp" CONTENT="Party time (seconds) from level upgrade to now"/>
	<String ID="LOGINFO_UserPartyCountSinceLivelUp" CONTENT="Number of parties since level upgrade"/>
	<String ID="LOGINFO_UserSelectCharacterInLobbyDesc" CONTENT="Log left when the user clicks Crecter in the lobby to start the game"/>
	<String ID="LOGINFO_UserClientIp" CONTENT="Connected client address"/>
	<String ID="LOGINFO_UserClientIpLast" CONTENT="The last placeholder in the connected client address"/>
	<String ID="LOGINFO_UserArbiter_LogoutDesc" CONTENT="The ip log left by Aviter_LogoutDesc when Crecter walks to the lobby or exits the game"/>
	<String ID="LOGINFO_UserDeadByPcDesc" CONTENT="Logs left behind when user dies on PC"/>
	<String ID="LOGINFO_UserDeadByNpcDesc" CONTENT="The log left by the user when he died from an NPC"/>
	<String ID="LOGINFO_KillerDbId" CONTENT="The DBID of the creation that killed the user"/>
	<String ID="LOGINFO_KillerClass" CONTENT="The class of the creation that kills the user"/>
	<String ID="LOGINFO_KillerLevel" CONTENT="The level of the creation that kills the user"/>
	<String ID="LOGINFO_KillerName" CONTENT="Name of the creation that killed the user"/>
	<String ID="LOGINFO_HuntingZoneId" CONTENT="Monster Hunter ID"/>
	<String ID="LOGINFO_TemplateID" CONTENT="Template ID of the monster"/>
	<String ID="LOGINFO_CreatureSize" CONTENT="Creature size"/>
	<String ID="LOGINFO_UserKillPcDesc" CONTENT="Log left when user kills other users"/>
	<String ID="LOGINFO_UserKillNpcDesc" CONTENT="Log left when user kills NPC"/>
	<String ID="LOGINFO_TargetDbId" CONTENT="target DBID"/>
	<String ID="LOGINFO_TargetClass" CONTENT="Target class"/>
	<String ID="LOGINFO_TargetLevel" CONTENT="Target level"/>
	<String ID="LOGINFO_TargetName" CONTENT="Target Name"/>
	<String ID="LOGINFO_CombatSec" CONTENT="Time it took for npc to die"/>
	<String ID="LOGINFO_UserEarnExpDesc" CONTENT="Logs left by users when they gain experience"/>
	<String ID="LOGINFO_QuestTemplateId" CONTENT="ID of the rewarded quest"/>
	<String ID="LOGINFO_QuestTaskIndex" CONTENT="Number of tasks performed in the task"/>
	<String ID="LOGINFO_EarnExp" CONTENT="Experience Earned"/>
	<String ID="LOGINFO_ResultExp" CONTENT="Total Experience"/>
	<String ID="LOGINFO_UserResurrectDesc" CONTENT="Log left when the user is resurrected"/>
	<String ID="LOGINFO_NewHp" CONTENT="New Hp"/>
	<String ID="LOGINFO_NewMp" CONTENT="New Mp"/>
	<String ID="LOGINFO_Damage" CONTENT="User Damage"/>
	<String ID="LOGINFO_UserFallingDamageDesc" CONTENT="Log of user falling damage"/>
	<String ID="LOGINFO_AttackerName" CONTENT="Name of my attacker"/>
	<String ID="LOGINFO_AttackerDbId" CONTENT="DbId of my attacker"/>
	<String ID="LOGINFO_UserDamageDesc" CONTENT="Logs left when users are attacked by users"/>
	<String ID="LOGINFO_ResurrectCasterName" CONTENT="Name of the character to be resurrected"/>
	<String ID="LOGINFO_AskResurrectDesc" CONTENT="Ask the named log of the resurrected character"/>
	<String ID="LOGINFO_FallingUnderTerrainDesc" CONTENT="The log of an endless fall under terrain"/>
	<String ID="LOGINFO_NewInventorySize" CONTENT="New inventory size"/>
	<String ID="LOGINFO_NewInventoryExpandCost" CONTENT="Inventory Expansion Cost"/>
	<String ID="LOGINFO_ExpandInventoryDesc" CONTENT="log remaining when expanding inventory"/>
	<String ID="LOGINFO_DeletedUserMoney" CONTENT="Amount held when deleted"/>
	<String ID="LOGINFO_DeletedUserMoneyDesc" CONTENT="Remaining logs in inventory when character is deleted"/>
	<String ID="LOGINFO_AchievementId" CONTENT="Achievement ID"/>
	<String ID="LOGINFO_AccomplishAchievementDesc" CONTENT="A log of achievements made by the character"/>
	<String ID="LOGINFO_PartyFirstCharacterDbId" CONTENT="Party Created Character DbId"/>
	<String ID="LOGINFO_PartyFirstCharacterName" CONTENT="Name of Cricketer who created the party"/>
	<String ID="LOGINFO_PartyLootionMethod" CONTENT="Project allocation method"/>
	<String ID="LOGINFO_CreatePartyDesc" CONTENT="Log from party creation"/>
	<String ID="LOGINFO_PartyManagerName" CONTENT="Party Name"/>
	<String ID="LOGINFO_PartyManagerDbId" CONTENT="PartyManagerDbId"/>
	<String ID="LOGINFO_JoinPartyDesc" CONTENT="Log left when entering the party"/>
	<String ID="LOGINFO_DisMissPartyDesc" CONTENT="Log left when the party venue disbanded"/>
	<String ID="LOGINFO_LeavePartyDesc" CONTENT="Logs left by users when they leave the party"/>
	<String ID="LOGINFO_KickedPartyDesc" CONTENT="Log from when a user was kicked out of a party"/>
	<String ID="LOGINFO_KickPartyDesc" CONTENT="Log left when the party leader forces other players to quit the party"/>
	<String ID="LOGINFO_KickedUserName" CONTENT="Name of player kicked from party"/>
	<String ID="LOGINFO_KickedUserDbid" CONTENT="Player Dbid kicked from party"/>
	<String ID="LOGINFO_OldManagerName" CONTENT="Name of previous party venue"/>
	<String ID="LOGINFO_OldManagerDbid" CONTENT="Last Banquet Dbid"/>
	<String ID="LOGINFO_ChangeManagerPartyDesc" CONTENT="Log left when party venue changed"/>
	<String ID="LOGINFO_ChangeLootingMethodDesc" CONTENT="Log left when party allocation method changes"/>
	<String ID="LOGINFO_GuildName" CONTENT="Guild Name"/>
	<String ID="LOGINFO_GuildDbId" CONTENT="GildDbId"/>
	<String ID="LOGINFO_GuildJoinMessage" CONTENT="Guild Join Message"/>
	<String ID="LOGINFO_CreateGuildDesc" CONTENT="The log left when the guild was created"/>
	<String ID="LOGINFO_ApplyGuildDesc" CONTENT="The log left when applying to join Guild"/>
	<String ID="LOGINFO_AcceptGuildDesc" CONTENT="The log left when applying to join Guild"/>
	<String ID="LOGINFO_RejectGuildDesc" CONTENT="The log left when the request to join Guild was rejected"/>
	<String ID="LOGINFO_JoinGuildDesc" CONTENT="The log left by becoming a guild member"/>
	<String ID="LOGINFO_DisMissGuildDesc" CONTENT="The log of Guild's dissolution"/>
	<String ID="LOGINFO_LeaveGuildDesc" CONTENT="The log left when leaving Guild"/>
	<String ID="LOGINFO_BannedGuildDesc" CONTENT="Log left when a user was banished from Guild"/>
	<String ID="LOGINFO_GuildBannedUserName" CONTENT="Guild's expelled user name"/>
	<String ID="LOGINFO_GuildBannedUserDbId" CONTENT="Player DbId expelled from Guild"/>
	<String ID="LOGINFO_GuildNewManagerName" CONTENT="New Guild Name"/>
	<String ID="LOGINFO_GuildNewManagerDbId" CONTENT="New Guild Hall DbId"/>
	<String ID="LOGINFO_GuildChangeManagerDesc" CONTENT="Log left when changing guild"/>
	<String ID="LOGINFO_GuildNewLevel" CONTENT="Guild level changed"/>
	<String ID="LOGINFO_GuildChangeLevelDesc" CONTENT="Log left when wrapping guild level"/>
	<String ID="LOGINFO_GuildRecommendedDesc" CONTENT="The log left when the guild recommended"/>
	<String ID="LOGINFO_AgitId" CONTENT="Secret base ID"/>
	<String ID="LOGINFO_Price" CONTENT="Price"/>
	<String ID="LOGINFO_AgitBuyDesc" CONTENT="A log left when purchasing a secret contact point"/>
	<String ID="LOGINFO_AgitRegistSellDesc" CONTENT="Log left when selling registered secret base"/>
	<String ID="LOGINFO_AgitUpdateSellDesc" CONTENT="Updating the log left by the secret point of contact sale"/>
	<String ID="LOGINFO_AgitCancelSellDesc" CONTENT="Log left when selling the Secret Contact Point"/>
	<String ID="LOGINFO_AgitSellComment" CONTENT="Sales Comment"/>
	<String ID="LOGINFO_OldValue" CONTENT="Old Value"/>
	<String ID="LOGINFO_NewValue" CONTENT="Changed value"/>
	<String ID="LOGINFO_UserProfMineralDesc" CONTENT="Log left when user's ore proficiency changes"/>
	<String ID="LOGINFO_UserProfBugDesc" CONTENT="Log left when the user's insect proficiency changes"/>
	<String ID="LOGINFO_UserProfEnergyDesc" CONTENT="Log left when a user's general proficiency changes"/>
	<String ID="LOGINFO_UserProfHerbDesc" CONTENT="Log left when user's plant proficiency changes"/>
	<String ID="LOGINFO_UserProfPetDesc" CONTENT="Log left when a user's Pet proficiency changes"/>
	<String ID="LOGINFO_UserSkillProfDesc" CONTENT="Log left when a user's crafting skill proficiency changes"/>
	<String ID="LOGINFO_UserSkillProfId" CONTENT="Crafting Skill Id"/>
	<String ID="LOGINFO_CollectionTryDesc" CONTENT="Log left when user picks up collection *start*"/>
	<String ID="LOGINFO_CollectionSuccessDesc" CONTENT="The log left when the user picks up the collection *successfully*"/>
	<String ID="LOGINFO_CollectionFailDesc" CONTENT="Log left when the user picks up the collection *failed*"/>
	<String ID="LOGINFO_CollectionCancelDesc" CONTENT="Log left when the user picks up the collection *cancel*"/>
	<String ID="LOGINFO_CollectionTemplateId" CONTENT="CollectionId"/>
	<String ID="LOGINFO_CollectionNeededProf" CONTENT="(collection skill required for target collection) *requires* proficiency"/>
	<String ID="LOGINFO_CollectionCurruntProf" CONTENT="(collection skill required for target collection) *current*proficiency"/>
	<String ID="LOGINFO_CollectedItemTemplateId" CONTENT="Collected Delivery Item TemplateID"/>
	<String ID="LOGINFO_Amount" CONTENT="Amount"/>
	<String ID="PrevPage" CONTENT="PrevPage"/>
	<String ID="NextPage" CONTENT="NextPage"/>
	<String ID="LOGINFO_LT_CHEATER_CHECKED_ON_CLIENT_Desc" CONTENT="Log left when client thinks it's a container"/>
	<String ID="LOGINFO_LT_CHEATER_PACKET_Desc" CONTENT="Log left when receiving abnormal packets that are suspected to be forged packets"/>
	<String ID="LOGINFO_LT_CHEATER_CONTENTS" CONTENT="Cure Possibilities"/>
	<String ID="LOGINFO_LT_PUNISH_SPEED_HACK" CONTENT="Speed ​​Nuclear Penalty Record"/>
	<String ID="LOGINFO_LT_CHEATER_LOCATION" CONTENT="Normal movement distance 1"/>
	<String ID="LOGINFO_LT_CHEATER_INVALID_MOVE_DISTANCE" CONTENT="Normal movement distance 2"/>
	<String ID="LOGINFO_LT_CHEATER_SPEED" CONTENT="Unsteady Average Moving Speed"/>
	<String ID="LOGINFO_LT_CHEATER_INVALID_MOVE_START_POS" CONTENT="Move start position abnormal"/>
	<String ID="LOGINFO_LT_CHEATER_ODD_SKILL_START_POS" CONTENT="Skill start position is abnormal"/>
	<String ID="LOGINFO_LT_CHEATER_MOVE_ON_INVALID_POS" CONTENT="Error shift"/>
	<String ID="LOGINFO_LT_CHEATER_TICK" CONTENT="Client Core"/>
	<String ID="LOGINFO_LT_CHEATER_NOTIFY_HACK" CONTENT="Use a specific cladding core"/>
	<String ID="LOGINFO_StartPos" CONTENT="Start Position"/>
	<String ID="LOGINFO_CurrPos" CONTENT="Current position"/>
	<String ID="LOGINFO_DestPos" CONTENT="Destination Position"/>
	<String ID="LOGINFO_PosDiff" CONTENT="Position Difference"/>
	<String ID="LOGINFO_ServerPos" CONTENT="ServerPos"/>
	<String ID="LOGINFO_ClientPos" CONTENT="Client Position"/>
	<String ID="LOGINFO_Speed" CONTENT="Movement Speed"/>
	<String ID="LOGINFO_OriginalSpeed" CONTENT="Normal Speed"/>
	<String ID="LOGINFO_ExceedSpeedRate" CONTENT="Overspeed (% value relative to normal speed)"/>
	<String ID="LOGINFO_AverageSpeed" CONTENT="Average Movement Speed"/>
	<String ID="LOGINFO_AccOriginalSpeed" CONTENT="Average Original Speed"/>
	<String ID="LOGINFO_AccExceedSpeedRate" CONTENT="Exceeded average speed (% of normal speed)"/>
	<String ID="LOGINFO_AccCountOfUseSpeedHack" CONTENT="Accumulated Speed ​​Core Use Times"/>
	<String ID="LOGINFO_AccDistanceOfUseSpeedHack" CONTENT="Accumulated Speed ​​Core Use Distance"/>
	<String ID="LOGINFO_AccTimeMsOfUseSpeedHack" CONTENT="Accumulated speed core usage time (milliseconds)"/>
	<String ID="LOGINFO_AccDistance" CONTENT="Accumulated moving distance"/>
	<String ID="LOGINFO_AccTimeMs" CONTENT="Accumulation time (milliseconds)"/>
	<String ID="LOGINFO_MoveTypeUser" CONTENT="Move Type (User)"/>
	<String ID="LOGINFO_MoveTypePacket" CONTENT="Move Type (Packet)"/>
	<String ID="LOGINFO_HackPunishmentType" CONTENT="Punishment Type"/>
	<String ID="LOGINFO_PunishRelativeValue" CONTENT="Punish Reference Value"/>
	<String ID="LOGINFO_TickLogType" CONTENT="TickLogType"/>
	<String ID="LOGINFO_ValidMargin" CONTENT="ValidMargin"/>
	<String ID="LOGINFO_OverMargin" CONTENT="Over Margin"/>
	<String ID="LOGINFO_NotifyHackType" CONTENT="Hack Type"/>
	<String ID="LOGINFO_CheatLevel" CONTENT="CheatLevel"/>
	<String ID="LOGINFO_CheatReason" CONTENT="CheatReason"/>
	<String ID="LOGINFO_PacketId" CONTENT="Packet ID"/>
	<String ID="LOGINFO_LT_AUDIT_INVALID_REGION_Desc" CONTENT="Log left when user's location error is detected"/>
	<String ID="NAVMENU_enterCount_reset_Short" CONTENT="Reset Instance Entry Count"/>
	<String ID="NAVMENU_enterCount_reset_Full" CONTENT="Reset Instance Count"/>
	<String ID="NAVMENU_user_pk_point_Short" CONTENT="UserPKPoint"/>
	<String ID="NAVMENU_user_pk_point_Full" CONTENT="Change UserPKPoint"/>
	<String ID="NAVMENU_rest_bonus_Short" CONTENT="Rest Point"/>
	<String ID="NAVMENU_rest_bonus_Full" CONTENT="Set Rest Point"/>
	<String ID="NAVMENU_TradeLog_Short" CONTENT="Trade Log"/>
	<String ID="NAVMENU_TradeLog_Log" CONTENT="Trade Log"/>
	<String ID="NAVMENU_Reputation_Short" CONTENT="Reputation"/>
	<String ID="NAVMENU_Reputation_Full" CONTENT="Reputation"/>
	<String ID="NAVMENU_GuildWarManagement_Short" CONTENT="Guild War Management"/>
	<String ID="NAVMENU_GuildWarManagement_Full" CONTENT="Guild War Management"/>
	<String ID="NAVMENU_GuildWarConfiguration_Short" CONTENT="Guild War Settings"/>
	<String ID="NAVMENU_GuildWarConfiguration_Full" CONTENT="Guild War Settings"/>
	<String ID="NAVMENU_GuildWarRanking_Short" CONTENT="Guild WarRanking"/>
	<String ID="NAVMENU_GuildWarRanking_Full" CONTENT="Guild WarRanking"/>
	<String ID="NAVMENU_GuildFloatingCastle_Short" CONTENT="Floating Guild"/>
	<String ID="NAVMENU_GuildFloatingCastle_Full" CONTENT="Floating Guild"/>
	<String ID="NAVMENU_GuildFloatingCastlePartsStoreState_Short" CONTENT="Parts Store"/>
	<String ID="NAVMENU_GuildFloatingCastlePartsStoreState_Full" CONTENT="Parts Store"/>
	<String ID="SearchGuildWar" CONTENT="Search Guild War"/>
	<String ID="TradeDate" CONTENT="TradeDate"/>
	<String ID="TargetUser" CONTENT="Target User"/>
	<String ID="LT_USER_DECREASE_PKPOINT" CONTENT="Decrease PK points"/>
	<String ID="PkPointDecreaseReason" CONTENT="Why reduce PK points"/>
	<String ID="Take" CONTENT="Take"/>
	<String ID="Give" CONTENT="Lifecycle"/>
	<String ID="LOGINFO_LT_DUNGEON_PARTY_MATCH_ADD_TO_POOL" CONTENT="Apply for instance matching"/>
	<String ID="LOGINFO_LT_DUNGEON_PARTY_MATCH_DEL_FROM_POOL" CONTENT="Delete instance match"/>
	<String ID="LOGINFO_LT_DUNGEON_PARTY_MATCH_FIN" CONTENT="Complete instance matching"/>
	<String ID="LOGINFO_LT_DUNGEON_PARTY_MATCH_FIN_FOR_MANAGER" CONTENT="Finish the party between servers"/>
	<String ID="PartyMatchPoolType" CONTENT="PartyMatchPoolType"/>
	<String ID="DungeonId" CONTENT="DungeonId"/>
	<String ID="SupportUser" CONTENT="Support User"/>
	<String ID="PartyMatchSuccessOrNot" CONTENT="PartyMatchSuccessOrNot"/>
	<String ID="PartyUserRole" CONTENT="Party User Role"/>
	<String ID="UserItemLevel" CONTENT="Equipment Level"/>
	<String ID="ElapsedSec" CONTENT="Elapsed time (seconds)"/>
	<String ID="RoundTime" CONTENT="RoundTime (seconds)"/>
	<String ID="PartyMananger" CONTENT="PartyMananger"/>
	<String ID="SentMoney" CONTENT="Sent Money"/>
	<String ID="ReceivedMoney" CONTENT="Received Money"/>
	<String ID="NAVMENU_TroubleShoot_Short" CONTENT="Troubleshooting"/>
	<String ID="NAVMENU_TroubleShoot_Full" CONTENT="Troubleshooting"/>
	<String ID="NAVMENU_TSTask1_Short" CONTENT="2011-11-22"/>
	<String ID="NAVMENU_TSTask1_Full" CONTENT="Tool to resolve issues with items being paid to certain users due to improper AdminReward operation"/>
	<String ID="AdminRewardedItemsInfo" CONTENT="Rewarded Items Info"/>
	<String ID="AdminRewardedItemAmount" CONTENT="Amount Rewarded"/>
	<String ID="CurrentRelatedItemAmount" CONTENT="Currently held total"/>
	<String ID="LT_USER_MEDALSTORE_ITEM_BUY" CONTENT="Log for each item while shopping at the Medal Shop"/>
	<String ID="LT_USER_MEDALSTORE_ITEM_BUYEND" CONTENT="Pay Medallions when shopping at Medal Shop"/>
	<String ID="MedalTemplateId" CONTENT="Medal ID"/>
	<String ID="MedalAmount" CONTENT="Amount of Medals"/>
	<String ID="DungeonAllReset" CONTENT="DungeonAllReset"/>
	<String ID="DungeonAllResetAlert" CONTENT="Please confirm the name of the server to do a full reset of the Dungeon"/>
	<String ID="ReputationInfo" CONTENT="ReputationInfo"/>
	<String ID="NpcGuildName" CONTENT="NpcGuildName"/>
	<String ID="ReputationGrade" CONTENT="ReputationGrade"/>
	<String ID="ReputationGradeName" CONTENT="ReputationGradeName"/>
	<String ID="ReputationExp" CONTENT="Reputation Experience"/>
	<String ID="GradeTable" CONTENT="Prestige Rating Table"/>
	<String ID="ChangeReputationExp" CONTENT="Change ReputationExp"/>
	<String ID="ConfirmChangeReputationExp" CONTENT="ConfirmChangeReputationExp"/>
	<String ID="ConfirmChangeReputationPoint" CONTENT="ConfirmChangeReputationPoint"/>
	<String ID="ReputationPoint" CONTENT="Reputation Point"/>
	<String ID="ChangeReputationPoint" CONTENT="Change Reputation Point"/>
	<String ID="ExpectResultReputationExp" CONTENT="Reputation experience to change"/>
	<String ID="ExpectResultReputationPoint" CONTENT="Reputation point to change"/>
	<String ID="AddNewNpcGuild" CONTENT="Add Judgement"/>
	<String ID="ConfirmAddNewNpcGuild" CONTENT="Please check and judge the power, level, experience value"/>
	<String ID="NewReputationResult" CONTENT="New Reputation Force"/>
	<String ID="Amount" CONTENT="Amount"/>
	<String ID="Action" CONTENT="Action"/>
	<String ID="Deposit" CONTENT="Archive"/>
	<String ID="Withdraw" CONTENT="Withdraw"/>
	<String ID="NAVMENU_GuildWarehouseLog_Full" CONTENT="Guild Warehouse Log"/>
	<String ID="NAVMENU_GuildWarehouseLog_Short" CONTENT="Guild Warehouse Log"/>
	<String ID="NpcReturn" CONTENT="NPC session"/>
	<String ID="LOGINFO_LT_AUTO_PARTY_MATCHING" CONTENT="Auto In-Server Party Matching"/>
	<String ID="LOGINFO_LT_DARK_RIFT_QUEST" CONTENT="Dark Rift Quest Log"/>
	<String ID="LOGINFO_LT_DARK_RIFT_EVENT_ZONE" CONTENT="Dark Rift Event Zone Log"/>
	<String ID="LOGINFO_LT_DARK_RIFT_INSTANCE" CONTENT="Dark Rift Instance"/>
	<String ID="AutoPartyMatchPoolId" CONTENT="AutoPartyMatchPoolId"/>
	<String ID="AutoPartyMatchAccepted" CONTENT="Accepted"/>
	<String ID="AutoPartyMatchRejected" CONTENT="Rejected"/>
	<String ID="DarkRift" CONTENT="Dark Rift"/>
	<String ID="DarkRiftEventZoneId" CONTENT="DarkRiftEventZoneId"/>
	<String ID="DarkRiftInstanceTemplateId" CONTENT="DarkRiftInstanceTemplateId"/>
	<String ID="DarkRiftInstanceId" CONTENT="DarkRiftInstanceId"/>
	<String ID="QuestTemplateId" CONTENT="QuestTemplateId"/>
	<String ID="DarkRiftQuestDifficultyType" CONTENT="Difficulty"/>
	<String ID="DarkRiftCurrentStep" CONTENT="Stage"/>
	<String ID="DifficultySmall" CONTENT="Small"/>
	<String ID="DifficultySolo" CONTENT="Solo"/>
	<String ID="DifficultyParty" CONTENT="Party"/>
	<String ID="DifficultyUnion" CONTENT="Union Party"/>
	<String ID="StartQuest" CONTENT="Start Quest"/>
	<String ID="SuccessQuest" CONTENT="Success"/>
	<String ID="FailByNoPoint" CONTENT="Failed due to insufficient points"/>
	<String ID="FailByOutOfRange" CONTENT="Failed out of range"/>
	<String ID="CancelQuest" CONTENT="CancelQuest"/>
	<String ID="StartDarkRiftBySystem" CONTENT="StartDarkRiftBySystem"/>
	<String ID="StartDarkRiftByItem" CONTENT="StartDarkRiftByItem"/>
	<String ID="StartDarkRiftByAdmin" CONTENT="StartDarkRiftByAdmin"/>
	<String ID="CloseDarkRiftByTimeout" CONTENT="CloseDarkRiftByTimeout"/>
	<String ID="CloseDarkRiftByUser" CONTENT="CloseDarkRiftByUser"/>
	<String ID="CloseDarkRiftByAdmin" CONTENT="CloseDarkRiftByAdmin"/>
	<String ID="CloseDarkRiftByChannelDestroyed" CONTENT="CloseDarkRiftByChannelDestroyed"/>
	<String ID="ProgressDarkRiftStep" CONTENT="ProgressDarkRiftStep"/>
	<String ID="ExpandDarkRiftArea" CONTENT="ExpandDarkRiftArea"/>
	<String ID="FoldDarkRiftArea" CONTENT="FoldDarkRiftArea"/>
	<String ID="DarkRiftExpandStage" CONTENT="DarkRiftExpandStage"/>
	<String ID="DarkRiftQuestStartedUserCount" CONTENT="DarkRiftQuestStartedUserCount"/>
	<String ID="DarkRiftUserCountInQuestRange" CONTENT="DarkRiftUserCountInQuestRange"/>
	<String ID="Created" CONTENT="Created"/>
	<String ID="Destroyed" CONTENT="Destroyed"/>
	<String ID="Progress" CONTENT="Progress"/>
	<String ID="Reason" CONTENT="Reason"/>
	<String ID="NAVMENU_DarkRiftHuntingZoneOnOff_Short" CONTENT="Nexus Setup"/>
    <String ID="NAVMENU_DarkRiftHuntingZoneOnOff_Full" CONTENT="Nexus Hunting Grounds On/Off"/>
    <String ID="NAVMENU_DarkRiftInstanceOnOff_Short" CONTENT="Nexus On/Off "/>
    <String ID="NAVMENU_DarkRiftInstanceOnOff_Full" CONTENT="Nexus Dungeons"/>
	<String ID="DarkRiftHuntingZoneEnabled" CONTENT="Enable/Disable Nexus Hunting Zones"/>
    <String ID="DarkRiftInstanceControl" CONTENT="Nexus Dungeon Control"/>
	<String ID="ReputationPointStatus" CONTENT="ReputationPointStatus"/>
	<String ID="RequestReputationPoint" CONTENT="RequestReputationPoint"/>
	<String ID="PointObtainReason" CONTENT="PointObtainReason"/>
	<String ID="BeforeReputationExp" CONTENT="BeforeReputationExp"/>
	<String ID="AfterReputationExp" CONTENT="AfterReputationExp"/>
	<String ID="DiffReputationExp" CONTENT="DiffReputationExp"/>
	<String ID="AccReputationExp" CONTENT="AccReputationExp"/>
	<String ID="RequestReputationExp" CONTENT="RequestReputationExp"/>
	<String ID="PointByQuest" CONTENT="PointByQuest"/>
	<String ID="PointByAchievement" CONTENT="PointByAchievement"/>
	<String ID="PointByHunt" CONTENT="PointByHunting"/>
	<String ID="PointByItem" CONTENT="PointByItem"/>
	<String ID="PointByBattleField" CONTENT="PointByBattlefield"/>
	<String ID="PointBySkillAffection" CONTENT="PointBySkillAffection"/>
	<String ID="PointByAdmin" CONTENT="PointByAdmin"/>
	<String ID="PointByUnionPk" CONTENT="PointByUnionPK"/>
	<String ID="PointByPlayGuide" CONTENT="PointByPlayGuide"/>
	<String ID="PointByUnknown" CONTENT="PointByUnknown"/>
	<String ID="LT_USER_REPUTATION_POINTSTORE_BUY" CONTENT="Items purchased with Prestige Points"/>
	<String ID="LT_OBTAIN_REPUTATION_POINT" CONTENT="Get Prestige Points"/>
	<String ID="LT_CONSUME_REPUTATION_POINT" CONTENT="Reputation Points Reduction"/>
	<String ID="LT_CHANGE_REPUTATION_EXP" CONTENT="Reputation EXP Change"/>
	<String ID="LT_OPERATOR_CHANGE_REPUTATION_EXP" CONTENT="Modify Operation Tool Prestige EXP"/>
	<String ID="LT_OPERATOR_CHANGE_REPUTATION_POINT" CONTENT="Modify Operation Tool Evaluation Point"/>
	<String ID="QuestCategory" CONTENT="Quest Type"/>
	<String ID="QT_NORMAL" CONTENT="Normal Task"/>
	<String ID="QT_MISSION" CONTENT="Mission Task"/>
	<String ID="QT_GUILD" CONTENT="Guild Tasks"/>
	<String ID="QT_DAILY" CONTENT="Daily Task"/>
	<String ID="QT_UNION" CONTENT="Union Mission"/>
	<String ID="QT_DARK_RIFT" CONTENT="DARK_RIFT"/>
	<String ID="LT_USER_QUEST_TASK_END" CONTENT="End Task"/>
	<String ID="LT_USER_QUEST_DROP_BY_SYSTEM" CONTENT="System Delete Task"/>
	<String ID="LT_USER_QUEST_START" CONTENT="Start Task"/>
	<String ID="LT_USER_QUEST_END" CONTENT="End Task"/>
	<String ID="LT_USER_QUEST_CANCEL" CONTENT="Cancel Task"/>
	<String ID="Invalid_Reputation_Point" CONTENT="Reputation point value entered is out of valid range."/>
	<String ID="Invalid_Reputation_Exp" CONTENT="The reputation rating or experience you entered is out of the valid range."/>
	<String ID="DarkRiftNpcRespawn" CONTENT="Respawning NPCs in the Nexus"/>
    <String ID="NAVMENU_DarkRiftNpcRespawn_Short" CONTENT="Respawning NPCs in the Nexus"/>
    <String ID="NAVMENU_DarkRiftNpcRespawn_Full" CONTENT="Respawning NPCs in the Nexus"/>
    <String ID="DarkRiftNotExists" CONTENT="Nexus does not exist at the selected location."/>
	<String ID="Open" CONTENT="Open"/>
	<String ID="Reserve" CONTENT="Reserve"/>
	<String ID="NAVMENU_DarkRiftPkSectionOnOff_Short" CONTENT="Dark Rift PK Control"/>
    <String ID="NAVMENU_DarkRiftPkSectionOnOff_Full" CONTENT="Dark Rift PK Control"/>
    <String ID="DarkRiftPkSectionOnOff" CONTENT="Dark Rift PK Control"/>
	<String ID="NAVMENU_HuntingzoneEventSet_Short" CONTENT="HuntingZone Event Settings"/>
	<String ID="NAVMENU_HuntingzoneEventSet_Full" CONTENT="HuntingZone Event Settings"/>
	<String ID="HuntingzoneEventSet" CONTENT="Huntingzone Event"/>
	<String ID="HuntingzoneEventSetTitle" CONTENT="[HuntingzoneEventSetTitle]"/>
	<String ID="AddHuntingzoneEvent" CONTENT="Adding a hunting location to the hunting zone event settings"/>
	<String ID="WorldFestivalHuntingZone" CONTENT="Select Hunting Grounds (up to 10)"/>
	<String ID="WorldFestivalAddHuntingZone" CONTENT="Add Hunting Zone"/>
	<String ID="AlertCheckAllServer" CONTENT="When set to replica or entire server, check all servers"/>
	<String ID="AddHuntingzoneEvent_Confirm" CONTENT="Do you want to register for hunting zone events?"/>
	<String ID="AllField" CONTENT="All"/>
	<String ID="NormalField" CONTENT="Normal"/>
	<String ID="DungeonField " CONTENT="Dungeon"/>
	<String ID="HuntingzoneEventValue" CONTENT="HuntingzoneEventValue"/>
	<String ID="HuntingzoneEventDelete" CONTENT="HuntingzoneEventDelete"/>
	<String ID="DeleteAllEvent" CONTENT="DeleteAllEvent"/>
	<String ID="HuntingzoneEventList" CONTENT="Event Type"/>
	<String ID="HuntingzoneEventTypeDropRate" CONTENT="Increase Item Gain Rate"/>
	<String ID="HuntingzoneEventTypeGoldAmountRate" CONTENT="Increase Gold Gain Rate"/>
	<String ID="HuntingzoneEventTypeExpRate" CONTENT="Increase Experience Gain Rate"/>
	<String ID="HuntingzoneEventTypeItemDropRate" CONTENT="Increase Enhancement Success Rate"/>
	<String ID="NAVMENU_GoldAmountRateEventSet_Short" CONTENT="Gold Amount Rate Event Set"/>
	<String ID="NAVMENU_GoldAmountRateEventSet_Full" CONTENT="Gold Amount Rate Event Set"/>
	<String ID="GoldAmountRateEventSet" CONTENT="Set the percentage increase in the number of gold drawers"/>
	<String ID="GoldAmountRateEventSetTitle" CONTENT="[Can set gold drawer increment (multiple)]"/>
	<String ID="GoldAmountRateEventValue" CONTENT="Gold-Drab Amount Increase Rate Multiplier"/>
	<String ID="GoldAmountEventDelete" CONTENT="GoldAmountEventDelete"/>
	<String ID="EventPeriod" CONTENT="EventPeriod"/>
	<String ID="EventId" CONTENT="Event ID"/>
	<String ID="DungeonCooltimeEventValue" CONTENT="DungeonCooltimeEventValue"/>
	<String ID="ScheduledWorldFestivalTitle" CONTENT="[Event Scheduling Setting Function]"/>
	<String ID="StyleInfo" CONTENT="StyleInfo"/>
	<String ID="Numbers" CONTENT="Numbers"/>
	<String ID="SealState" CONTENT="SealState"/>
	<String ID="PeriodKind" CONTENT="Period Limit Type"/>
	<String ID="GetItemDate" CONTENT="GetItemDate"/>
	<String ID="RemainTime" CONTENT="Expiration Date"/>
	<String ID="PeriodInfo" CONTENT="Period Info"/>
	<String ID="ScheduledTime" CONTENT="ScheduledTime"/>
	<String ID="ScheduledServer" CONTENT="ScheduledServer"/>
	<String ID="LT_USER_ENTER_INSTANCE_DUNGEON" CONTENT="Enter Dungeon"/>
	<String ID="LT_USER_EXIT_INSTANCE_DUNGEON" CONTENT="Exit DUNGEON"/>
	<String ID="LT_USER_LEAVE_INSTANCE_DUNGEON" CONTENT="Exit Dungeon"/>
	<String ID="DungeonName" CONTENT="Dungeon Name"/>
	<String ID="Gold" CONTENT="Gold"/>
	<String ID="Silver" CONTENT="Silver"/>
	<String ID="Cooper" CONTENT="Cooper"/>
	<String ID="NoSearchOption" CONTENT="No Search Option"/>
	<String ID="UserQuestWarning" CONTENT="Please verify that your quest information is correct"/>
	<String ID="Player" CONTENT="Player"/>
	<String ID="LT_PERIODITEM_EXPIRE" CONTENT="Limited Time Item Expiration"/>
	<String ID="LT_PERIODITEM_COMPOSITE" CONTENT="Periodic Project Portfolio"/>
	<String ID="LT_PERIODITEM_CREATE" CONTENT="Create Period Project"/>
	<String ID="NAVMENU_DarkRiftControlReservation_Short" CONTENT="Nexus Time Control"/>
	<String ID="NAVMENU_DarkRiftControlReservation_Full" CONTENT="Nexus Time Control"/>
	<String ID="DarkRiftControlReservation" CONTENT="Nexus Time Control"/>
	<String ID="DayOfWeek" CONTENT="DayOfWeek"/>
	<String ID="DarkRiftReservationTime" CONTENT="Nexus Reservation Status"/>
	<String ID="CurrentStamina" CONTENT="STAMINA"/>
	<String ID="NAVMENU_DungeonClearInfo_Short" CONTENT="Dungeon Clear Info"/>
	<String ID="NAVMENU_DungeonClearInfo_Full" CONTENT="Number of times to clear instances"/>
	<String ID="InstanceDungeonLevelLimit" CONTENT="InstanceDungeonLevelLimit"/>
	<String ID="InstanceDungeonName" CONTENT="InstanceDungeonName"/>
	<String ID="InstanceDungeonClearTime" CONTENT="InstanceDungeonClearTime"/>
	<String ID="InstanceDungeonProf" CONTENT="proficiency level"/>
	<String ID="InstanceDungeonProfType1" CONTENT="Proficient"/>
	<String ID="InstanceDungeonProfType2" CONTENT="Immature"/>
	<String ID="TeamMasterName" CONTENT="TeamMasterName"/>
	<String ID="OccupyStrongHold" CONTENT="Occupy Stronghold"/>
	<String ID="ChangeDungeonClearCount" CONTENT="Modify Dungeon ClearCount"/>
	<String ID="DungeonClearCount" CONTENT="Modify Dungeon ClearCount"/>
	<String ID="ConfirmDungeonClearCount" CONTENT="Confirm Dungeon Clear Count"/>
	<String ID="AttackUnit" CONTENT="Attack Unit"/>
	<String ID="NormalParty" CONTENT="General Party"/>
	<String ID="AutoMatching" CONTENT="AutoMatching"/>
	<String ID="PrivatePoint" CONTENT="Personal Score"/>
	<String ID="TeamPoint" CONTENT="Team Score"/>
	<String ID="LT_BATTLE_FIELD_GAIN_POINT" CONTENT="Battlefield Score (Create)"/>
	<String ID="LT_BATTLE_FIELD_TEAM_GAIN_POINT" CONTENT="Battlefield Team Score"/>
	<String ID="ReasonAddPoint" CONTENT="Reason for Score"/>
	<String ID="MonsterHunting" CONTENT="Monster Hunting"/>
	<String ID="LT_BATTLE_FIELD_MATCH_ADD_TO_POOL" CONTENT="Apply for Battlefield Match"/>
	<String ID="LT_BATTLE_FIELD_MATCH_DEL_FROM_POOL" CONTENT="Cancel Battleground Match"/>
	<String ID="LT_BATTLE_FIELD_MATCH_FIN" CONTENT="Complete Battlefield Match"/>
	<String ID="LT_BATTLE_FIELD_MATCH_FIN_FOR_MANAGER" CONTENT="Complete Battlefield Match between Servers"/>
	<String ID="LT_CHANGE_AUTHORITY_PARTY" CONTENT="Attack group invitation permission"/>
	<String ID="BattleFieldMatchType" CONTENT="BattleFieldMatchType"/>
	<String ID="TeamMasterAssist" CONTENT="Partiparchy Support"/>
	<String ID="UserPartyRole" CONTENT="UserPartyRole"/>
	<String ID="UserElapsedTime" CONTENT="User Elapsed Time (seconds)"/>
	<String ID="CancelCheck" CONTENT="Cancel"/>
	<String ID="PartyMasterDbId" CONTENT="PartyMasterDbId"/>
	<String ID="PartyMasterServerName" CONTENT="PartyMasterServerName"/>
	<String ID="PartyMemberServerName" CONTENT="PartyMemberServerName"/>
	<String ID="PartyUserID" CONTENT="PartyUserID"/>
	<String ID="InviteAuth" CONTENT="InviteAuth"/>
	<String ID="InviteAuthAddUser" CONTENT="Authorizer"/>
	<String ID="Grant" CONTENT="Grant"/>
	<String ID="Deprivation" CONTENT="Deprivation"/>
	<String ID="NAVMENU_Restore_Short" CONTENT="Restore Operation"/>
	<String ID="NAVMENU_Restore_Full" CONTENT="Restore Item/Money"/>
	<String ID="NAVMENU_ViewRestoreCandidate_Short" CONTENT="Restoration"/>
	<String ID="NAVMENU_ViewRestoreCandidate_Full" CONTENT="Restore Item/Money"/>
	<String ID="RestoreItemMoney" CONTENT="Restoration"/>
	<String ID="NAVMENU_ViewRestoreItemMoney_Short" CONTENT="ViewRestoreItemMoney"/>
	<String ID="NAVMENU_ViewRestoreItemMoney_Full" CONTENT="Restore Item/Money by Operator"/>
	<String ID="NAVMENU_ViewRestoreItemMoneyByUser_Short" CONTENT="ViewRestoreItemMoneyByUser"/>
	<String ID="NAVMENU_ViewRestoreItemMoneyByUser_Full" CONTENT="Restore Item/Money by User"/>
	<String ID="ChangeBOTConfiguration" CONTENT="Change BOT Configuration"/>
	<String ID="DeleteBOTCandidateAccount" CONTENT="Delete BOT Candidate Account"/>
	<String ID="AddBOTCandidateAccount" CONTENT="Add BOT Candidate Account"/>
	<String ID="NAVMENU_DetectBOT_Short" CONTENT="DetectBOT"/>
	<String ID="NAVMENU_DetectBOT_Full" CONTENT="Detect BOT Configuration"/>
	<String ID="NAVMENU_BOTConfiguration_Short" CONTENT="BOT Configuration"/>
	<String ID="NAVMENU_BOTConfiguration_Full" CONTENT="Change BOT Configuration"/>
	<String ID="NAVMENU_ViewBOTCandidateAccount_Short" CONTENT="View BOT Candidates"/>
	<String ID="NAVMENU_ViewBOTCandidateAccount_Full" CONTENT="View/Delete BOT Candidates"/>
	<String ID="Private" CONTENT="Private"/>
	<String ID="INVTYPE_ACCESSORY_HAIR" CONTENT="Hair"/>
	<String ID="INVTYPE_ACCESSORY_FACE" CONTENT="Face"/>
	<String ID="INVTYPE_STYLE_BODY" CONTENT="Style Body"/>
	<String ID="LT_USER_POLITICS_POINTSTORE_BUY" CONTENT="Items purchased as strategy points"/>
	<String ID="LT_SET_FLAG" CONTENT="LT_SET_FLAG"/>
	<String ID="FLAGTYPE" CONTENT="FLAGTYPE"/>
	<String ID="LordRank" CONTENT="LordRank"/>
	<String ID="LordBehaviorPoint" CONTENT="LordBehaviorPoint"/>
	<String ID="ByAdmin" CONTENT="ByAdmin"/>
	<String ID="LT_ACCOMPLISH_LORD_ACHIEVEMENT" CONTENT="Lord Achievement Log"/>
	<String ID="NAVMENU_LordAchievementInfoPage_Short" CONTENT="Lord Achievement Info Page"/>
	<String ID="NAVMENU_LordAchievementInfoPage_Full" CONTENT="Lord Achievement Info Page"/>
	<String ID="NAVMENU_LordAchievementRank_Short" CONTENT="Lord Achievement Rank"/>
	<String ID="NAVMENU_LordAchievementRank_Full" CONTENT="Permanent Resident Activity Score Information"/>
	<String ID="AccumulatedPolicyPoint" CONTENT="AccumulatedPolicyPoint"/>
	<String ID="NpcActivation" CONTENT="NpcActivation"/>
	<String ID="Recommendation" CONTENT="Recommendation"/>
	<String ID="LordFlag" CONTENT="Lord Flag"/>
	<String ID="LordAchievementList" CONTENT="Lord Achievement List"/>
	<String ID="ChangeLordAchievementData" CONTENT="ChangeLordAchievementData"/>
	<String ID="AcquiredLordBehaviorPoint" CONTENT="AcquiredLordBehaviorPoint"/>
	<String ID="NewLordBehaviorPoint" CONTENT="NewLordBehaviorPoin"/>
	<String ID="LordAchievementDataType" CONTENT="LordAchievementDataType"/>
	<String ID="NAVMENU_UpdateNotification_Short" CONTENT="Update Notification"/>
	<String ID="NAVMENU_UpdateNotification_Full" CONTENT="Update Notification"/>
	<String ID="RegistUpdateNotification" CONTENT="Register Update Notification"/>
	<String ID="ConfirmRegistUpdateNotification" CONTENT="Please register for update notifications."/>
	<String ID="RegistUpdateNotificationView" CONTENT="View Update Notification"/>
	<String ID="ConfirmRegistTitleUpdateNotification" CONTENT="Please register the announcement title."/>
	<String ID="ConfirmRegistContentUpdateNotification" CONTENT="Please register notification content."/>
	<String ID="EnchantAdjustment" CONTENT="Enhancing Adjustment Value"/>
	<String ID="LT_ITEM_ENCHANT_ADJUSTMENT_CHANGE" CONTENT="Enhanced Failure Correction Value"/>
	<String ID="SPT_NORMAL" CONTENT="No single TEXT"/>
	<String ID="SPT_NEW_PRESENT" CONTENT="The email sent by the system after the new project is created"/>
	<String ID="SPT_MOVE" CONTENT="When the system moves an existing object and sends an email"/>
	<String ID="SPT_ESCROW" CONTENT="Password sent by the system"/>
	<String ID="LT_SEND_SYSTEM_PARCEL" CONTENT="Send mail in the system"/>
	<String ID="LT_BATTLE_FIELD_JACKPOT_START" CONTENT="Start Battleground JackPod Event"/>
	<String ID="BattleFieldName" CONTENT="BattleFieldName"/>
	<String ID="JackPotMission" CONTENT="JackPotMission"/>
	<String ID="WinJackPotMissionAward" CONTENT="WinJackPotMissionAward"/>
	<String ID="LT_BATTLE_FIELD_RECEIVE_JACKPOT_REWARD" CONTENT="Pay Battleground Jack Potter quest reward"/>
	<String ID="WinPoint" CONTENT="WinPoint"/>
	<String ID="ExcelDownload" CONTENT="Download Excel"/>
	<String ID="CurrentRankScore" CONTENT="CurrentRankScore"/>
	<String ID="Score" CONTENT="Score"/>
	<String ID="LT_ROUND_BATTLE_FIELD_WIN" CONTENT="Round PVP Battlefield Victory"/>
	<String ID="LT_ROUND_BATTLE_FIELD_LOSE" CONTENT="Failed in round PVP battle"/>
	<String ID="LT_ROUND_BATTLE_FIELD_DRAW" CONTENT="Round PVP Battleground Draw"/>
	<String ID="GameResult" CONTENT="Game Result"/>
	<String ID="InsanitySlayerPoint" CONTENT="InsanitySlayerPoint"/>
	<String ID="VictoryIntroducerPoint" CONTENT="Victory IntroducerPoint"/>
	<String ID="LT_USER_BATTLE_FIELD_POINTSTORE_BUY" CONTENT="Items purchased as battlefield judgement points"/>
	<String ID="TargetNPC" CONTENT="Target NPC"/>
	<String ID="PurchaseItem" CONTENT="Purchased Item"/>
	<String ID="LT_USER_CLEAR_RANK_DUNGEON" CONTENT="Record competition"/>
	<String ID="DungeonClearRank" CONTENT="Ranking"/>
	<String ID="KillPoint" CONTENT="KillPoint"/>
	<String ID="TimePoint" CONTENT="TimePoint"/>
	<String ID="BonusPoint" CONTENT="Bonus Points"/>
	<String ID="ClearPoint" CONTENT="Clear Point"/>
	<String ID="NAVMENU_Dungeon_Short" CONTENT="Dungeon Management"/>
	<String ID="NAVMENU_Dungeon_Full" CONTENT="Dungeon Management"/>
	<String ID="NAVMENU_DungeonRanking_Short" CONTENT="Dungeon Ranking"/>
	<String ID="NAVMENU_DungeonRanking_Full" CONTENT="Dungeon Ranking"/>
	<String ID="DungeonChoice" CONTENT="DungeonChoice"/>
	<String ID="SeasonChoice" CONTENT="SeasonChoice"/>
	<String ID="SearchRankResult" CONTENT="History query results"/>
	<String ID="DungeonBestRank" CONTENT="Dungeon Best Rank"/>
	<String ID="DungeonBestPoint" CONTENT="Dungeon Best Point"/>
	<String ID="DungeonBestAccPoint" CONTENT="Dungeon Best Accumulative Point"/>
	<String ID="DungeonBestScoreDate" CONTENT="Dungeon Best Score Date"/>
	<String ID="DungeonaccPointBestRank" CONTENT="Accumulated Records"/>
	<String ID="RegistUpdateNotificationViewAll" CONTENT="View full update notifications"/>
	<String ID="LT_ACCOMPLISH_ACHIEVEMENT" CONTENT="Achievement"/>
	<String ID="LT_USER_CLEAR_RANK_DUNGEON_COMPENSATION" CONTENT="Record Competitive Cluster Compensation"/>
	<String ID="LT_BATTLE_FIELD_JACKPOT_EXPECT_ITEM" CONTENT="The Battlefield JackPot event item will be compensated"/>
	<String ID="LT_BATTLE_FIELD_JACKPOT_EXPECT_WINPOINT" CONTENT="Battlefield JackPod Event Program Points Compensation"/>
	<String ID="LT_BATTLE_FIELD_JACKPOT_FINISH" CONTENT="Complete Battleground JackPot Event"/>
	<String ID="LT_BATTLE_FIELD_JACKPOT_REWARD_ITEM" CONTENT="Battlefield JackPot Event Item Compensation"/>
	<String ID="LT_BATTLE_FIELD_JACKPOT_REWARD_WINPOINT" CONTENT="Battlefield JackPod Event Product Compensation"/>
	<String ID="WinPointIncreaseRate" CONTENT="Point Increase Rate"/>
	<String ID="DungeonServerStatus" CONTENT="Dungeon Server Status"/>
	<String ID="PartyMatchServerStatus" CONTENT="Party Match Server Status"/>
	<String ID="NexusServerStatus" CONTENT="Nexus Status"/>
	<String ID="NAVMENU_ServerMonitorLog_Short" CONTENT="Server Monitor Log"/>
	<String ID="NAVMENU_ServerMonitorLog_Full" CONTENT="Server Monitor Log"/>
	<String ID="EnterLimitCount" CONTENT="Number of entries"/>
	<String ID="HuntingzoneEventTypeDungeonCooltime" CONTENT="Reduce Dungeon Cooldown Multiplier"/>
	<String ID="HuntingzoneEventTypeDungeonClearCount" CONTENT="Increase Dungeon Clear Count"/>
	<String ID="Log_UnionId" CONTENT="AllianceID"/>
	<String ID="Union" CONTENT="Alliance"/>
	<String ID="SelectAllUnion" CONTENT="Select All Alliances"/>
	<String ID="LT_UNION_JOIN_UNION" CONTENT="Registration for the Alliance"/>
	<String ID="LT_UNION_LEAVE_UNION" CONTENT="Leaving the Alliance"/>
	<String ID="LT_UNION_REG_CONSUL_CANDIDATE" CONTENT="Registration of manager candidacy"/>
	<String ID="LT_UNION_CANCEL_CONSUL_CANDIDATE" CONTENT="Candidacy registration cancellation"/>
	<String ID="LT_UNION_ELECT_CONSUL" CONTENT="Manager selected"/>
	<String ID="LT_UNION_FAILED_CONSUL" CONTENT="No manager selected"/>
	<String ID="LT_UNION_CHANGE_TAX_RATE" CONTENT="Change tax ratio"/>
	<String ID="LT_UNION_TAKE_TAX" CONTENT="Receiving Alliance taxes"/>
	<String ID="LT_UNION_CHANGE_CLASS" CONTENT="Change position"/>
	<String ID="LT_UNION_APPOINT_COMMANDER" CONTENT="Commander Appointment"/>
	<String ID="LT_UNION_DISMISS_COMMANDER" CONTENT="Dismissal of the commander"/>
	<String ID="LT_UNION_SHARE_ACTIVITY_COST" CONTENT="Activity payment distribution"/>
	<String ID="LT_UNION_SHARED_ACTIVITY_COST" CONTENT="Receiving payment for activities"/>
	<String ID="LT_UNION_ADD_CP_TO_UNION" CONTENT="Receiving Alliance Contribution"/>
	<String ID="LT_UNION_ADD_CP_TO_GUILD" CONTENT="Receiving Guild Alliance Contribution"/>
	<String ID="LT_UNION_ADD_CP_TO_MEMBER" CONTENT="Receiving a personal Contribution"/>
	<String ID="LT_UNION_SEASON_BEGIN" CONTENT="Season Beginning"/>
	<String ID="LT_UNION_SEASON_END" CONTENT="Season End"/>
	<String ID="LT_UNION_EXTRACTOR_NUM_CHANGED" CONTENT="Changing the number of extractors"/>
	<String ID="LT_UNION_OBTAIN_EXTRACTOR_ITEM" CONTENT="Receiving an extractor item"/>
	<String ID="LT_UNION_STEAL_EXTRACTOR_ITEM" CONTENT="Return of the extractor item"/>
	<String ID="LT_UNION_DROP_ITEM" CONTENT="Alliance item drop"/>
	<String ID="LT_UNION_START_CONSULE_BUFF" CONTENT="Start of positive manager effect"/>
	<String ID="LT_UNION_POLICY_POINT_DECREASE" CONTENT="Policy Point Reduction"/>
	<String ID="LT_UNION_DISMISS_GUILD" CONTENT="Exclusion from the guild"/>
	<String ID="LT_UNION_USE_POLICY_ITEM" CONTENT="Policy Change"/>
	<String ID="LT_UNION_POWER_USAGE" CONTENT="Use of manager powers"/>
	<String ID="LT_UNION_SHARE_POLICY_POINT" CONTENT="Distribution of manager policy points"/>
	<String ID="LT_UNION_SHARE_POLICY_POINT_TO_PERSON" CONTENT="Receiving policy points from the manager"/>
	<String ID="LT_UNION_POLICY_POINT_INCREASE" CONTENT="Getting Alliance policy points"/>
	<String ID="LT_UNION_TROUBLED_AREA_QUEST" CONTENT="Getting policy points through conflict territory quests"/>
	<String ID="LT_UNION_ARCHIEVE_LEVEL" CONTENT="Receiving policy points for an Alliance member when reaching a certain level"/>
	<String ID="LT_UNION_SET_TAX_RATE" CONTENT="Policy points generated when setting tax ratio"/>
	<String ID="LT_UNION_POLICY_POINT_BY_EXTRACTOR_COUNT" CONTENT="Getting policy points from the number of Alliance extractors at a certain time interval"/>
	<String ID="LT_UNION_KILL_OTHER_UNION_USER" CONTENT="Whenever a member of another Alliance is killed"/>
	<String ID="LT_UNION_SPEND_PERSONAL_POLICY_POINT" CONTENT="Personal policy point expenditure"/>
	<String ID="LT_UNION_MAKE_ITEM" CONTENT="Making Alliance items"/>
	<String ID="LT_UNION_USE_SKILL" CONTENT="Reducing policy points by using Alliance skills"/>
	<String ID="LT_UNION_GAIN_PERSONAL_POLICY_POINT" CONTENT="Gain personal policy points"/>
	<String ID="LT_UNION_GET_POLICY_POINT_FROM_CONSUL" CONTENT="Getting policy points awarded by the manager"/>
	<String ID="LT_UNION_START_POST_BATTLE" CONTENT="Start of the siege of the Alliance base"/>
	<String ID="LT_UNION_JOIN_POST_BATTLE" CONTENT="Participation in the siege of an Alliance base"/>
	<String ID="LT_UNION_END_POST_BATTLE" CONTENT="End the siege of the Alliance base"/>
	<String ID="LT_UNION_ENTER_LEAVE_POST_BATTLE" CONTENT="Enter/exit from the siege of the Alliance base"/>
	<String ID="LT_UNION_ROB_TAX" CONTENT="Theft of Alliance base taxes"/>
	<String ID="LT_UNION_SEASON_RANKING_RESULT" CONTENT="Alliance Battle Season Ranking"/>
	<String ID="LT_UNION_POST_BATTLE_RESULT" CONTENT="Result of the Alliance Battle"/>
	<String ID="LT_UNION_POST_BATTLE_REWARD" CONTENT="Alliance Battle Reward"/>
	<String ID="UnionPostBattleAttack" CONTENT="ID of the attacking Alliance"/>
	<String ID="UnionPostBattleDefence" CONTENT="ID of the defending Alliance"/>
	<String ID="UnionPostBattleJoin" CONTENT="ID of the participating Alliance"/>
	<String ID="UnionPostBattleRob" CONTENT="ID of the invading Alliance"/>
	<String ID="UnionPostBattleResultAttackWin" CONTENT="Successful attack"/>
	<String ID="UnionPostBattleResultDefenceWin" CONTENT="Successful defense"/>
	<String ID="UnionPostBattleEnter" CONTENT="Entrance to the siege of the base"/>
	<String ID="UnionPostBattleLeave" CONTENT="Leaving the base siege"/>
	<String ID="UnionPostBattlePost" CONTENT="Base Siege Alliance ID"/>
	<String ID="UnionPostBattleUser" CONTENT="User Alliance ID"/>
	<String ID="UnionPostBattleRobMoney" CONTENT="Stolen parameter"/>
	<String ID="UnionPostBattleRobBefore" CONTENT="Parameter before abduction"/>
	<String ID="UnionPostBattleRobAfter" CONTENT="Post-kidnapping parameter"/>
	<String ID="UnionPostBattleRecover" CONTENT="Possibility of return"/>
	<String ID="UnionPostBattleTime" CONTENT="Battle Time"/>
	<String ID="UnionDBID" CONTENT="Alliance DBID"/>
	<String ID="UnionName" CONTENT="Alliance Name"/>
	<String ID="UnionClass" CONTENT="Position in the alliance"/>
	<String ID="ConsecutiveWin" CONTENT="Straight wins"/>
	<String ID="UnionPostBattlePoint" CONTENT="Alliance Battle Points"/>
	<String ID="TaxRateModify" CONTENT="Changing tax rate"/>
	<String ID="AppointDuty" CONTENT="Appointed Duty"/>
	<String ID="DismissDuty" CONTENT="Dismissal from position"/>
	<String ID="TotalCost" CONTENT="Total Cost"/>
	<String ID="PrivateCost" CONTENT="Private Cost"/>
	<String ID="SharedCostUser" CONTENT="User who received payment for the activity"/>
	<String ID="UnionCP" CONTENT="Alliance Contribution"/>
	<String ID="UnionWholeCP" CONTENT="Accumulated alliance contribution"/>
	<String ID="RCP_LEVELUP" CONTENT="Change level"/>
	<String ID="RCP_QUEST" CONTENT="QUEST"/>
	<String ID="RCP_NOC_TAKE" CONTENT="ruthenium"/>
	<String ID="RCP_PK" CONTENT="PK"/>
	<String ID="RCP_TAX" CONTENT="Tax"/>
	<String ID="RCP_ADMIN" CONTENT="Administrator"/>
	<String ID="UnionGeneration" CONTENT="Repeat alliance"/>
	<String ID="ExtractorNum" CONTENT="Number of extractors"/>
	<String ID="UnionBUFName" CONTENT="Name of alliance effect"/>
	<String ID="Consul" CONTENT="Manager"/>
	<String ID="PrivateCP" CONTENT="Personal Contribution"/>
	<String ID="ConsulDbId" CONTENT="Controller DBID"/>
	<String ID="GainPolicyPoint" CONTENT="Policy Points Gained"/>
	<String ID="NAVMENU_Union_Short" CONTENT="Alliances"/>
	<String ID="NAVMENU_Union_Full" CONTENT="Alliance Management"/>
	<String ID="NAVMENU_UnionSystemOnOff_Short" CONTENT="On/Off Alliance System"/>
	<String ID="NAVMENU_UnionSystemOnOff_Full" CONTENT="On/Off Alliance System"/>
	<String ID="ENABLE_UNION_POLITICS" CONTENT="Enable the alliance system"/>
	<String ID="DISABLE_UNION_POLITICS" CONTENT="Disable the alliance system"/>
	<String ID="ConfirmDisableUnionPolitic" CONTENT="Finish?"/>
	<String ID="ConfirmEnableUnionPolitic" CONTENT="Check start time?"/>
	<String ID="NAVMENU_UnionConsulInfo_Short" CONTENT="Information about the manager"/>
	<String ID="NAVMENU_UnionConsulInfo_Full" CONTENT="Information about the alliance manager"/>
	<String ID="NAVMENU_UnionCompetitionInfo_Short" CONTENT="Personal competition status"/>
	<String ID="NAVMENU_UnionCompetitionInfo_Full" CONTENT="Alliance personal competition status"/>
	<String ID="NAVMENU_UnionDungeonCompetitionInfo_Short" CONTENT="Training Camp Entry Competition Status"/>
	<String ID="NAVMENU_UnionDungeonCompetitionInfo_Full" CONTENT="Alliance Training Camp Entry Competition Status"/>
	<String ID="UnionChoice" CONTENT="Alliance Choice"/>
	<String ID="UnionSeason" CONTENT="Repeat alliance"/>
	<String ID="UnionInformation" CONTENT="Alliance Information"/>
	<String ID="ConsulName" CONTENT="ConsulName"/>
	<String ID="UnionGuild" CONTENT="Guild"/>
	<String ID="AssignedUnion" CONTENT="Alliance"/>
	<String ID="ConsulTerm" CONTENT="ConsulTerm"/>
	<String ID="UnionNoti" CONTENT="Alliance Announcement"/>
	<String ID="UnionPolicyInfo" CONTENT="Policy Description"/>
	<String ID="UnionTaxList" CONTENT="Tax profits accumulated in the alliance"/>
	<String ID="UnionPolicyPoint" CONTENT="Alliance Policy Points"/>
	<String ID="UnionSeasonCP" CONTENT="Alliance Seasonal Contribution"/>
	<String ID="MasterpieceActivation" CONTENT="Item Merchant Arrangement"/>
	<String ID="ModifyEnchantChance" CONTENT="Change Artisan Enhancement Chance"/>
	<String ID="UNION_DISMISS_CONSUL" CONTENT="Dismissal of a manager"/>
	<String ID="ConfirmUnionDismissConsul" CONTENT="Dismiss?"/>
	<String ID="UNION_CHANGE_NOTICE" CONTENT="Change alliance announcement"/>
	<String ID="ConfirmUnionChangeNotice" CONTENT="Change alliance announcement?"/>
	<String ID="NoticeInputConfirm" CONTENT="Enter announcement"/>
	<String ID="UNION_CHANGE_DESCRIPTION" CONTENT="Change policy description"/>
	<String ID="ConfirmUnionChangeDescription" CONTENT="Change policy description?"/>
	<String ID="DescriptionInputConfirm" CONTENT="Enter a policy description"/>
	<String ID="UNION_CHANGE_TAX" CONTENT="Change in profit from taxes accumulated in the alliance"/>
	<String ID="ConfirmUnionChangeTax" CONTENT="Change tax?"/>
	<String ID="TaxInputConfirm" CONTENT="Enter tax"/>
	<String ID="UNION_CHANGE_POLICY_POINT" CONTENT="Changing alliance policy points"/>
	<String ID="ConfirmUnionChangePolicyPoint" CONTENT="Change policy points?"/>
	<String ID="PolicyPointInputConfirm" CONTENT="Enter policy points"/>
	<String ID="NewPolicyPoint" CONTENT="Changeable alliance policy points"/>
	<String ID="UNION_CHANGE_TAX_RATE" CONTENT="Change alliance tax percentage"/>
	<String ID="ConfirmUnionChangeTaxRate" CONTENT="Change tax rate?"/>
	<String ID="TaxRateInputConfirm" CONTENT="Enter tax percentage"/>
	<String ID="NewTaxRate" CONTENT="Variable tax percentage"/>
	<String ID="UNION_CHANGE_MEMBER_CP" CONTENT="Adding a seasonal alliance contribution"/>
	<String ID="ConfirmUnionChangeMemberCP" CONTENT="Add seasonal contribution?"/>
	<String ID="MemberCPInputConfirm" CONTENT="Enter your seasonal contribution"/>
	<String ID="NewMemberCP" CONTENT="Seasonal contribution to be added"/>
	<String ID="NAVMENU_UnionInfo_Short" CONTENT="Alliance Information"/>
	<String ID="NAVMENU_UnionInfo_Full" CONTENT="Alliance Information"/>
	<String ID="UnionAppoint" CONTENT="Appointment"/>
	<String ID="UnionBan" CONTENT="Exception"/>
	<String ID="UnionDismiss" CONTENT="Dismissal"/>
	<String ID="UNION_CHANGE_POLICY" CONTENT="Change alliance policy"/>
	<String ID="ConfirmUnionChangePolicy" CONTENT="Change policy?"/>
	<String ID="UNION_CHANGE_CONSUL_SKILL" CONTENT="Changing the Alliance Power Skill"/>
	<String ID="ConfirmUnionChangeConsulSkill" CONTENT="Change ConsulSkill?"/>
	<String ID="RepresentTemplateId" CONTENT="Manager authority ID"/>
	<String ID="UNION_BAN_GUILD" CONTENT="Exclude guild from alliance"/>
	<String ID="ConfirmUnionBanGuild" CONTENT="Exclude?"/>
	<String ID="UNION_CANCEL_CONSUL_CANDIDATE" CONTENT="Cancel manager candidate"/>
	<String ID="UNION_DISMISS_COMMANDER" CONTENT="Dismissal from the position of commander"/>
	<String ID="ConfirmUnionDismissCommander" CONTENT="Dismiss?"/>
	<String ID="ConfirmUnionCancelConsulCandidate" CONTENT="Cancel registration of candidacy?"/>
	<String ID="UNION_CHANGE_CLASS" CONTENT="Assign to a position in the alliance"/>
	<String ID="UNION_APPOINT_COMMANDER" CONTENT="Assign to the position of commander"/>
	<String ID="ConfirmUnionAppointCommander" CONTENT="Appoint position?"/>
	<String ID="NAVMENU_UnionDungeonInfo_Short" CONTENT="Training Camp Login Information"/>
	<String ID="NAVMENU_UnionDungeonInfo_Full" CONTENT="Training Camp Login Information"/>
	<String ID="UNION_CHANGE_DUNGEON_SCHDULE" CONTENT="Changing training camp login information"/>
	<String ID="ConfirmUnionChangeDungeonSchdule" CONTENT="Change?"/>
	<String ID="ConfirmUnionChangeClass" CONTENT="Change?"/>
	<String ID="Time" CONTENT="Time"/>
	<String ID="DungeonEnterPossible" CONTENT="Alliances that can enter the training camp"/>
	<String ID="NAVMENU_UserUnion_Short" CONTENT="Alliance Information"/>
	<String ID="NAVMENU_UserUnion_Full" CONTENT="Alliance Information"/>
	<String ID="UnionDuty" CONTENT="Position in the alliance"/>
	<String ID="UnionMemberPolicyPoint" CONTENT="Personal Policy Points"/>
	<String ID="UnionMemberSeasonCP" CONTENT="Personal Seasonal Contribution"/>
	<String ID="UnionMemberTotalCP" CONTENT="Accumulated personal contribution"/>
	<String ID="REQUEST_CHANGE_UNIONMEMBER_PP" CONTENT="Changing personal alliance policy points"/>
	<String ID="ConfirmMemberPP" CONTENT="Change personal policy points?"/>
	<String ID="MemberPPInputConfirm" CONTENT="Enter your personal policy points"/>
	<String ID="NewMemberPP" CONTENT="Changable Personal Policy Points"/>
	<String ID="REQUEST_CHANGE_UNIONMEMBER_CP" CONTENT="Changing personal alliance contribution"/>
	<String ID="ConfirmUnionMemberCP" CONTENT="Change personal contribution?"/>
	<String ID="UnionMemberCPInputConfirm" CONTENT="Enter your personal contribution"/>
	<String ID="NewUnionMemberCP" CONTENT="Modifiable personal contribution"/>
	<String ID="REQUEST_CHANGE_UNIONMEMBER_ACCUM_CP" CONTENT="Changing the accumulated personal contribution of the alliance"/>
	<String ID="ConfirmUnionMemberAccumCP" CONTENT="Change accumulated alliance personal contribution?"/>
	<String ID="UnionMemberAccumCPInputConfirm" CONTENT="Enter your accumulated personal contribution"/>
	<String ID="NewUnionMemberAccumCP" CONTENT="Changeable accumulated personal contribution"/>
	<String ID="TargetUnion" CONTENT="Target Union"/>
	<String ID="UnionJoinInfo" CONTENT="Alliance registration information"/>
	<String ID="ChiefDuty" CONTENT="Guild Leader Position"/>
	<String ID="GuildSeasonCP" CONTENT="Guild Seasonal Contribution"/>
	<String ID="GuildTotalCP" CONTENT="Accumulated guild contribution"/>
	<String ID="REQUEST_CHANGE_GUILD_CP" CONTENT="Changing the guild's seasonal contribution to the alliance"/>
	<String ID="ConfirmGuildCP" CONTENT="Change guild season contribution?"/>
	<String ID="GuildCPInputConfirm" CONTENT="Enter the seasonal guild contribution"/>
	<String ID="NewGuildCP" CONTENT="Variable seasonal guild contribution"/>
	<String ID="REQUEST_CHANGE_GUILD_ACCUM_CP" CONTENT="Changing the accumulated guild contribution to the alliance"/>
	<String ID="ConfirmGuildAccumCP" CONTENT="Change the accumulated guild contribution?"/>
	<String ID="GuildAccumCPInputConfirm" CONTENT="Enter the accumulated guild contribution"/>
	<String ID="NewGuildAccumCP" CONTENT="Changeable guild accumulative contribution"/>
	<String ID="NAVMENU_UnionStateInfo_Short" CONTENT="Information about Parliament"/>
	<String ID="NAVMENU_UnionStateInfo_Full" CONTENT="Information about Parliament"/>
	<String ID="UnionStateInfo" CONTENT="UnionStateInfo"/>
	<String ID="ChangeAdminToolStateInfo" CONTENT="ChangeAdminToolStateInfo"/>
	<String ID="Setting_battle_field" CONTENT="Setting Battlefield"/>
	<String ID="Setting_battle_field_Open" CONTENT="Battlefield On"/>
	<String ID="Setting_battle_field_Close" CONTENT="Battlefield Off"/>
	<String ID="LT_USER_USE_NOCTAN" CONTENT="Use Green Charcoal"/>
	<String ID="NoctanCount" CONTENT="Number of Green Charcoal"/>
	<String ID="LT_CASHBUFF_STOP" CONTENT="Storing paid effect"/>
	<String ID="LT_CASHBUFF_RESTART" CONTENT="Re-enabling a paid effect"/>
	<String ID="LT_CASHBUFF_END" CONTENT="End of paid effect"/>
	<String ID="Cashbuff_Name" CONTENT="Charging Bucket Name"/>
	<String ID="Cashbuff_Id" CONTENT="Cashbuff ID"/>
	<String ID="Remained_Miliseconds" CONTENT="Remaining time (ms)"/>
	<String ID="UnionCompetitionInfoList" CONTENT="Entry competition statistics"/>
	<String ID="Abnormality" CONTENT="Abnormality"/>
	<String ID="HoldedAbnormality" CONTENT="HoldedAbnormality"/>
	<String ID="AbnormalityID" CONTENT="AbnormalityID"/>
	<String ID="AbnormalityName" CONTENT="AbnormalityName"/>
	<String ID="RemainedTime" CONTENT="Remaining time (minutes)"/>
	<String ID="StackCount" CONTENT="StackCount"/>
	<String ID="ValueRate" CONTENT="ValueRate"/>
	<String ID="DeleteAbnormality" CONTENT="DeleteAbnormality"/>
	<String ID="AddAbnormality" CONTENT="AddAbnormality"/>
	<String ID="SharedWorldDb" CONTENT="SharedWorldDb"/>
	<String ID="MaxSlotCount" CONTENT="MaxSlotCount"/>
	<String ID="TCatRegCost" CONTENT="Tcat Registration"/>
	<String ID="TCatSoldCost" CONTENT="Tcat calculation"/>
	<String ID="NAVMENU_Abnormality_Short" CONTENT="Abnormality"/>
	<String ID="NAVMENU_Abnormality_Full" CONTENT="View Abnormality Details"/>
	<String ID="DEL_ABNORMALITY" CONTENT="Delete Abnormality"/>
	<String ID="ADD_ABNORMALITY" CONTENT="Add Abnormality"/>
	<String ID="CHANGE_ABNORMALITY_REMAINTIME" CONTENT="Change Abnormality Remaining Time"/>
	<String ID="AbnormalityToDelete" CONTENT="AbnormalityToDelete"/>
	<String ID="ConfirmDeleteAbnormality" CONTENT="ConfirmDeleteAbnormality"/>
	<String ID="AbnormalityToAdd" CONTENT="AbnormalityToAdd"/>
	<String ID="ConfirmAddAbnormality" CONTENT="ConfirmAddAbnormality"/>
	<String ID="EnterNewTCatAmount" CONTENT="Enter the number of Tcats to change"/>
	<String ID="NewTCatAmount" CONTENT="New TCat Amount"/>
	<String ID="ChangeTCatAmount" CONTENT="Change TCat Amount"/>
	<String ID="ConfirmChangeTCatAmount" CONTENT="Confirm Change TCat Amount"/>
	<String ID="ChangedTCatAmount" CONTENT="Changed TCat Amount"/>
	<String ID="CHANGE_TCAT" CONTENT="Change TCat"/>
	<String ID="NAVMENU_InGameStore_Short" CONTENT="Game Store"/>
	<String ID="NAVMENU_InGameStore_Full" CONTENT="Game Store"/>
	<String ID="NAVMENU_InGameStoreView_Short" CONTENT="TCat Store"/>
	<String ID="NAVMENU_InGameStoreView_Full" CONTENT="TCat Store"/>
	<String ID="StopInGameStore" CONTENT="Close TCat Store"/>
	<String ID="StartInGameStore" CONTENT="Open TCat Store"/>
	<String ID="ChangeBattleFieldInfo" CONTENT="ChangeBattleFieldInfo"/>
	<String ID="ChangeBattleFieldDetails" CONTENT="Change Battle Field Details"/>
	<String ID="ChangeBattleFieldGradeScore" CONTENT="Change BattleField Grade Score"/>
	<String ID="ChangeStrongHoldBattleFieldInfo" CONTENT="Change StrongHold BattleField Info"/>
	<String ID="ChangeStrongHoldBattleFieldDetails" CONTENT="Change StrongHold BattleField Details"/>
	<String ID="CountOfWin" CONTENT="Win"/>
	<String ID="CountOfDraw" CONTENT="Draw"/>
	<String ID="CountOfLose" CONTENT="Lose"/>
	<String ID="CountOfKill" CONTENT="Kill"/>
	<String ID="CountOfDeath" CONTENT="Death"/>
	<String ID="CountOfAssist" CONTENT="Assist"/>
	<String ID="RoundPvPGradeScore" CONTENT="Score"/>
	<String ID="StrongHoldCountOfWin" CONTENT="Win"/>
	<String ID="StrongHoldCountOfDraw" CONTENT="Draw"/>
	<String ID="StrongHoldCountOfLose" CONTENT="Lose"/>
	<String ID="StrongHoldCountOfKill" CONTENT="Kill"/>
	<String ID="StrongHoldCountOfDeath" CONTENT="Death"/>
	<String ID="StrongHoldCountOfAssist" CONTENT="Assist"/>
	<String ID="Remained_Time" CONTENT="Remaining time (minutes/hours)"/>
	<String ID="LT_F2P_USEITEM_INCLEVEL" CONTENT="Increase item usage level"/>
	<String ID="LT_F2P_USEITEM_INCEXP" CONTENT="Increase item experience value"/>
	<String ID="LT_TRANSLATE_TCAT_ITEM" CONTENT="TCat Transform"/>
	<String ID="LT_USE_ITEM_SKILL" CONTENT="Launch item use skills"/>
	<String ID="LT_TS_CHANGE_SHAREDDBTCAT" CONTENT="TCat quantity change"/>
	<String ID="LT_INGAMESTORE_BUY_COIN_PRODUCT" CONTENT="Buy Game Store Coin Products"/>
	<String ID="LT_INGAMESTORE_BUY_TCAT_PRODUCT" CONTENT="Buy Tcat merchandise in game store"/>
	<String ID="LT_OPERATOR_DEL_SUSPENDED_ABNORMALITY" CONTENT="Delete Operator BUFF Status"/>
	<String ID="LT_OPERATOR_DEL_HOLDED_ABNORMALITY" CONTENT="Delete Operator Cumulative BUFF Status"/>
	<String ID="LT_OPERATOR_ADD_ABNORMALITY" CONTENT="Add operator BUFF status"/>
	<String ID="LT_OPERATOR_CHANGE_SHAREDTCATAMOUNT" CONTENT="Operator TCat Change"/>
	<String ID="LT_OPERATOR_ADDDELTA_SHAREDTCATAMOUNT" CONTENT="Add Tcat Administrator"/>
	<String ID="LT_OPERATOR_ASK_INGAMESTORE" CONTENT="Play store status as operator"/>
	<String ID="LT_OPERATOR_STOP_INGAMESTORE" CONTENT="Stopped Game Store"/>
	<String ID="LT_OPERATOR_START_INGAMESTORE" CONTENT="Starting Game Store"/>
	<String ID="InstantBuyYes" CONTENT="InstantBuyYes"/>
	<String ID="LT_USER_EXPAND_INVENTORY" CONTENT="Extended Inventory"/>
	<String ID="LT_INCREASE_CHAR_SOCKET" CONTENT="[Server] Extended Character Slot"/>
	<String ID="LT_USE_GACHA" CONTENT="Use Gacha"/>
	<String ID="LT_LOOT_GACHA_REWARD_ITEM" CONTENT="Loot Gacha Reward"/>
	<String ID="LT_USER_BUFF_ABNORMALITY" CONTENT="User Buff Abnormality"/>
	<String ID="BENEFIT_PACKAGE_34" CONTENT="Frontier"/>
	<String ID="BENEFIT_PACKAGE_33" CONTENT="Pitch point"/>
	<String ID="BENEFIT_PACKAGE_32" CONTENT="Inden Cool Time"/>
	<String ID="BENEFIT_PACKAGE_31" CONTENT="Expedition Reward"/>
	<String ID="BENEFIT_PACKAGE_1000" CONTENT="Internet Cafe"/>
	<String ID="GiveItemToConcurrentUserTitle" CONTENT="Mail Items to All Current Online Users"/>
	<String ID="NAVMENU_GiveItemToConcurrentUser_Short" CONTENT="Give Items to All Online Users"/>
	<String ID="NAVMENU_GiveItemToConcurrentUser_Full" CONTENT="Give Items to All Players Online by Mail"/>
	<String ID="GiveItemViaMail" CONTENT="Give Item Via Mail"/>
	<String ID="CheckYourMailBox" CONTENT="Registered. Please check your mailbox."/>
	<String ID="WaitConfirmation" CONTENT="Registered. Will reflect upon approval."/>
	<String ID="ConfirmInput" CONTENT="Do you want to register for your input?"/>
	<String ID="SetChannelRestrictLevelTitle" CONTENT="Set Chat Restriction Level"/>
	<String ID="NAVMENU_SetChannelRestrictLevel_Short" CONTENT="Set Chat Restriction Level"/>
	<String ID="NAVMENU_SetChannelRestrictLevel_Full" CONTENT="Set Chat Restriction Level"/>
	<String ID="ShowScheduledAnnounceList" CONTENT="Show Scheduled Announcement List"/>
	<String ID="IndexNumber" CONTENT="IndexNumber"/>
	<String ID="Second" CONTENT="Second"/>
	<String ID="CheckCurrentSetting" CONTENT="Check Current Setting"/>
	<String ID="LT_INGAMESTORE_REQUESTBUY_COIN_PRODUCT" CONTENT="Request currency product"/>
	<String ID="Unknown" CONTENT="Pending"/>
	<String ID="QuestCompletedMoreThanOnce" CONTENT="Repeat Completed task"/>
	<String ID="ScheduledAnnounceTime" CONTENT="Scheduled Announce Time"/>
	<String ID="UnionTaxAdjustRate" CONTENT="Alliance Tax Adjustment Rate: "/>
	<String ID="CHANGE_UNION_TAX_ADJUST_RATE" CONTENT="Change alliance tax change rate"/>
	<String ID="CHANGE_TAX_ADJUST_RATE" CONTENT="Change Rate"/>
	<String ID="UnionTaxRobAdjustRate" CONTENT="Alliance tax robbery change rate: "/>
	<String ID="CHANGE_UNION_TAX_ROB_ADJUST_RATE" CONTENT="Alliance Tax Robbery Rate Changes"/>
	<String ID="CHANGE_TAX_ROB_ADJUST_RATE" CONTENT="Change Tax Robbery"/>
	<String ID="AddEventHuntingBonusItem" CONTENT="Add NPC Drop Item"/>
	<String ID="DelEventHuntingBonusItem" CONTENT="Delete NPC Drop Item"/>
	<String ID="ModifyEventHuntingBonusItem" CONTENT="Modify NNPC Drop Item"/>
	<String ID="ButtonAddEventHuntingBonusItem" CONTENT="Add NPC Drop Item"/>
	<String ID="ButtonDelEventHuntingBonusItem" CONTENT="Delete NPC Drop Item"/>
	<String ID="ButtonModifyEventHuntingBonusItem" CONTENT="Modify NPC Drop Items"/>
	<String ID="NAVMENU_HuntingBonusItem_Short" CONTENT="Add NPC Drop Setting"/>
	<String ID="NAVMENU_HuntingBonusItem_Full" CONTENT="NPC Hunting Bonus Prize Event"/>
	<String ID="AddEventHuntingBonusItem_Confirm" CONTENT="Please ensure that the added target and item information is correct"/>
	<String ID="DelEventHuntingBonusItem_Confirm" CONTENT="Please confirm that the object and item information to be deleted is correct"/>
	<String ID="ModifyEventHuntingBonusItem_Confirm" CONTENT="Please confirm whether the object and item information to be modified is correct"/>
	<String ID="EventHuntingBonusItem_Begin" CONTENT="StartTime"/>
	<String ID="EventHuntingBonusItem_End" CONTENT="EndTime"/>
	<String ID="EventHuntingBonusItem_HuntingZone" CONTENT="Map"/>
	<String ID="EventHuntingBonusItem_NPC" CONTENT="NPC"/>
	<String ID="EventHuntingBonusItem_DropItem" CONTENT="Item ID"/>
	<String ID="EventHuntingBonusItem_EachDropAmount" CONTENT="Drop Amount"/>
	<String ID="EventHuntingBonusItem_Prob" CONTENT="Drop Probability"/>
	<String ID="EventHuntingBonusItem_DropLimit" CONTENT="Drop Limit (-1, no limit)"/>
	<String ID="EventHuntingBonusItem_IsOn" CONTENT="On/Off"/>
	<String ID="RegisterTeraTime" CONTENT="Register Tera Time"/>
	<String ID="CancelTeraTime" CONTENT="Cancel Tera Time"/>
	<String ID="NAVMENU_TeraTimeRegister_Short" CONTENT="Register Tera Time"/>
	<String ID="NAVMENU_TeraTimeRegister_Full" CONTENT="Distribute items to current online users"/>
	<String ID="NAVMENU_TeraTimeList_Short" CONTENT="Tera Time List"/>
	<String ID="NAVMENU_TeraTimeList_Full" CONTENT="Check and cancel item payment registration event"/>
	<String ID="RegisterMailEvent" CONTENT="Register Mail Event"/>
	<String ID="CancelMailEvent" CONTENT="[CancelMailEvent]"/>
	<String ID="NAVMENU_MailEventRegister_Short" CONTENT="Register Mail Event"/>
	<String ID="NAVMENU_MailEventRegister_Full" CONTENT="Add online time distribution items"/>
	<String ID="NAVMENU_MailEventList_Short" CONTENT="Mail Event List"/>
	<String ID="NAVMENU_MailEventList_Full" CONTENT="Mail Event List"/>
	<String ID="MailEvent" CONTENT="Mail Event"/>
	<String ID="SendIntervalMinute" CONTENT="Online time (minutes)"/>
	<String ID="MaxSendPerDay" CONTENT="Max Send Per Day"/>
	<String ID="LastItemTemplateId" CONTENT="LastItemTemplateId"/>
	<String ID="ItemProvideCount" CONTENT="ItemProvideCount"/>
	<String ID="LastItem" CONTENT="LastItem"/>
	<String ID="LastItemAmount" CONTENT="LastItemAmount"/>
	<String ID="NotEnoughSendCountForDay" CONTENT="When entering the last item, the number of available counts must be at least 2."/>
	<String ID="IncreaseAmount" CONTENT="Increase Amount"/>
	<String ID="Reserved" CONTENT="Reserved"/>
	<String ID="Cancelled" CONTENT="Cancelled"/>
	<String ID="Finished" CONTENT="Finished"/>
	<String ID="Expired" CONTENT="Expired"/>
	<String ID="OnProgress" CONTENT="OnProgress"/>
	<String ID="InputFormat" CONTENT="InputFormat"/>
	<String ID="ImmediateGiveItem" CONTENT="Immediately Send Mail Items"/>
	<String ID="ReserveGiveItem" CONTENT="Reserve Give Item Payment"/>
	<String ID="GiveItemPeriod" CONTENT="Give Item Period"/>
	<String ID="ReserveEvent" CONTENT="Reserve Event"/>
	<String ID="CancelEvent" CONTENT="Cancel Event"/>
	<String ID="PossibleAction" CONTENT="Possible Action"/>
	<String ID="CheckAdminPage" CONTENT="Check Admin Page."/>
	<String ID="NotifyExpiredConnection" CONTENT="Connection timed out, please log in again"/>
	<String ID="OverflowTeraTimeMaxCount" CONTENT="Exceeded maximum amount to pay"/>
	<String ID="NAVMENU_LimitedGacha_Short" CONTENT="Limited Gacha"/>
	<String ID="NAVMENU_LimitedGacha_Full" CONTENT="Limited Gacha"/>
	<String ID="Limited_Gacha_Total_Status" CONTENT="Gacha_Total_Status"/>
	<String ID="Limited_Gacha_Total_Bucket_Number" CONTENT="Total number of buckets"/>
	<String ID="Limited_Gacha_Left_Bucket_Number" CONTENT="Number of buckets remaining"/>
	<String ID="Limited_Gacha_Total_Left_Grace_Time" CONTENT="Minimum retention time remaining"/>
	<String ID="Limited_Gacha_Target_Item_List" CONTENT="Target Item List"/>
	<String ID="Limited_Gacha_Current_Bucket" CONTENT="Current Bucket"/>
	<String ID="Limited_Gacha_Select_Gacha" CONTENT="Select_Gacha"/>
	<String ID="Limited_Gacha_Start_Gacha" CONTENT="Start_Gacha"/>
	<String ID="Limited_Gacha_End_Gacha" CONTENT="End_Gacha"/>
	<String ID="Limited_Gacha_Modify_Gacha" CONTENT="Modify_Gacha"/>
	<String ID="Limited_Gacha_Search_Gacha" CONTENT="Search_Gacha"/>
	<String ID="Limited_Gacha_Count" CONTENT="Goal Count"/>
	<String ID="Limited_Gacha_Selected_Number" CONTENT="Selected optional number"/>
	<String ID="Limited_Gacha_Confirm_Gacha_Start" CONTENT="Would you like to run the corresponding earthwork control vehicle?"/>
	<String ID="Limited_Gacha_Use_Status" CONTENT="Quantity Control Gacha Usage"/>
	<String ID="Limited_Gacha_NowUse" CONTENT="In use"/>
	<String ID="Limited_Gacha_NowStop" CONTENT="Processing"/>
	<String ID="Limited_Gacha_Confirm_Gacha_Stop" CONTENT="Do you want to stop the corresponding earthwork control vehicle?"/>
	<String ID="Limited_Gacha_Confirm_Gacha_Modify" CONTENT="Apply this change history?"/>
	<String ID="Limited_Gacha_bucketId" CONTENT="period id"/>
	<String ID="Limited_Gacha_TargetItemLeftNumber" CONTENT="Number of remaining items"/>
	<String ID="LT_LIMITED_GACHA_ACQUISTION_PRE_DB" CONTENT="Limited Gacha has acquired items (before history)"/>
	<String ID="LT_LIMITED_GACHA_ACQUISTION_POST_DB" CONTENT="Limited Gacha has acquired item (after logging)"/>
	<String ID="LT_LIMITED_GACHA_CHAGE_BUCKET" CONTENT="Limited Gacha Hours Change Time"/>
	<String ID="LT_USER_MINIGAME_START" CONTENT="MiniGame Start"/>
	<String ID="LT_USER_MINIGAME_CLEAR" CONTENT="MiniGame Clear"/>
	<String ID="LT_USER_MINIGAME_CANCEL" CONTENT="MiniGame Cancel"/>
	<String ID="LT_USER_CLEAR_RANK_MINIGAME" CONTENT="Clear MiniGame Rank Records"/>
	<String ID="LT_USER_CLEAR_RANK_MINIGAME_COMPENSATION" CONTENT="Clear MiniGame Compesation Records"/>
	<String ID="MiniGameName" CONTENT="MiniGameName"/>
	<String ID="MiniGameId" CONTENT="MiniGameId"/>
	<String ID="LT_SYNTHESIZER_RESULT" CONTENT="Result of using synth"/>
	<String ID="LoadingScreenControl" CONTENT="Loading Screen Control"/>
	<String ID="LoadingScreenControlStatus" CONTENT="Loading Screen Control Status"/>
	<String ID="IsUsing" CONTENT="Busy"/>
	<String ID="IsNotUsing" CONTENT="Not Using"/>
	<String ID="Use" CONTENT="Use"/>
	<String ID="DontUse" CONTENT="Disable"/>
	<String ID="NAVMENU_ClientOp_Short" CONTENT="Client Settings"/>
	<String ID="NAVMENU_ClientOp_Full" CONTENT="Client Settings"/>
	<String ID="NAVMENU_LoadingScreenControl_Short" CONTENT="Loading Screen Control"/>
	<String ID="NAVMENU_LoadingScreenControl_Full" CONTENT="Loading Screen Control"/>
	<String ID="LoadingScreenControlChange" CONTENT="Change loading screen control state"/>
	<String ID="ConfirmChangeLoadingScreenStatus" CONTENT="Change loading screen control status?"/>
	<String ID="LT_ITEM_ENCHANT_BOOSTER_REGISTERED" CONTENT="Enchant Booster Registered"/>
	<String ID="TargetItem" CONTENT="TargetItem"/>
	<String ID="EnchantBoosterScrollItem" CONTENT="Enchant Booster ScrollItem"/>
	<String ID="EnchantBoosterPoint" CONTENT="Enhanced BoosterPoint"/>
	<String ID="EnchantBoosterMaxGrade" CONTENT="Enhanced Booster MaxGrade"/>
	<String ID="LT_TS_CHANGE_ENCHANT_BOOSTER" CONTENT="Change Enchant Booster"/>
	<String ID="NAVMENU_FestivalSeason_Short" CONTENT="Festival Event Control"/>
	<String ID="NAVMENU_FestivalSeason_Full" CONTENT="Festival Control"/>
	<String ID="FestivalName" CONTENT="Festival Name"/>
	<String ID="FestivalID" CONTENT="Festival ID"/>
	<String ID="FestivalStartDate" CONTENT="Festival Start Date"/>
	<String ID="FestivalEndDate" CONTENT="Festival End Date"/>
	<String ID="FestivalState" CONTENT="Festival State"/>
	<String ID="FestivalStart" CONTENT="Festival Start"/>
	<String ID="FestivalEnd" CONTENT="Festival End"/>
	<String ID="FestivalWait" CONTENT="Festival Wait"/>
	<String ID="FestivalEdit" CONTENT="Festival Edit"/>
	<String ID="FestivalNpcSpawn" CONTENT="Festival NpcSpawn"/>
	<String ID="FestivalNpcDeSpawn" CONTENT="Festival NpcDeSpawn"/>
	<String ID="FestivalDetail" CONTENT="Festival Detail"/>
	<String ID="FestivalModify" CONTENT="Festival Modify"/>
	<String ID="FestivalNpcModify" CONTENT="Festival NpcModify"/>
	<String ID="FestivalModifyResult" CONTENT="Festival Modify Result"/>
	<String ID="FestivalModifyError" CONTENT="Festival Modify Error"/>
	<String ID="FestivalModifySubmit" CONTENT="Festival Modify Submit"/>
	<String ID="FestivalNpcModifySubmit" CONTENT="Festival NpcModify Submit"/>
	<String ID="PenaltyRangeLow" CONTENT="Compensation (0-100) (low)"/>
	<String ID="PenaltyRangeHigh" CONTENT="Compensation (0-100) (high)"/>
	<String ID="PenaltyAccountDbIdToolTip" CONTENT="Search Account AccountDbId"/>
	<String ID="Compensation" CONTENT="Reward"/>
	<String ID="NAVMENU_NewBOTCandidateAccount_Short" CONTENT="New Penalty 100"/>
	<String ID="NAVMENU_NewBOTCandidateAccount_Full" CONTENT="New Penalty 100 BOT Candidates"/>
	<String ID="Refresh" CONTENT="Update"/>
	<String ID="EventHuntingBonus_NPC" CONTENT="NPC added"/>
	<String ID="Append_EventHuntingBonus_NPC" CONTENT="Add NPC"/>
	<String ID="EventHuntingBonus_BonusItem" CONTENT="Item added"/>
	<String ID="Append_EventHuntingBonus_BonusItem" CONTENT="Maximum 50 NPCs and 10 drop items can be added,"/>
	<String ID="Append_EventHuntingBonus_AddedResult" CONTENT="Number of event Bonus Item drop results"/>
	<String ID="DeleteFromList" CONTENT="Delete2"/>
	<String ID="AppendEventItem" CONTENT="Add item settings"/>
	<String ID="LT_TS_PREMIUMCOMPOSE" CONTENT="Premium Composition"/>
	<String ID="PremiumComposeRewardItem" CONTENT="Premium Compose Result"/>
	<String ID="PremiumComposeRewardItemAmount" CONTENT="PremiumComposeRewardItemAmount"/>
	<String ID="PremiumComposeMaterialItem" CONTENT="Premium ComposeMaterial"/>
	<String ID="LT_USER_DUNGEON_COOLTIME_RESET" CONTENT="Copy cooldown initialization related logs"/>
	<String ID="DungeonContinentIndex" CONTENT="Dungeon Continent Index"/>
	<String ID="RankingUpdateTime" CONTENT="Ranking Update Time"/>
	<String ID="NAVMENU_BoardManage_Short" CONTENT="Board Manager"/>
	<String ID="NAVMENU_BoardManage_Full" CONTENT="Manage Mountaineering Announcements"/>
	<String ID="BoardChoice" CONTENT="Board Choice"/>
	<String ID="BoardDbId" CONTENT="Board DbId"/>
	<String ID="BoardId" CONTENT="BoardId"/>
	<String ID="BoardName" CONTENT="Board Name"/>
	<String ID="BoardWriteTime" CONTENT="Board WriteTime"/>
	<String ID="BoardUserName" CONTENT="Board UserName"/>
	<String ID="BoardContent" CONTENT="Board Content"/>
	<String ID="BoardDelete" CONTENT="Board Delete"/>
	<String ID="ConfirmBoardDelete" CONTENT="Please confirm the post to delete"/>
	<String ID="LT_LIMITED_DROP_ACQUIRE_POINT" CONTENT="Get Quantity Control Points"/>
	<String ID="LimitedDropId" CONTENT="LimitedDropId"/>
	<String ID="LimitedDropPoint" CONTENT="Limited DropPoint"/>
	<String ID="LT_LIMITED_DROP_DROP_ITEMBAG" CONTENT="Limited Drop ItemBag"/>
	<String ID="LT_TOKEN_EXCHANGE_GET_TOKEN_POINT" CONTENT="Get points for automatic token exchange system"/>
	<String ID="TokenExchangeId" CONTENT="Automatic token exchange system id"/>
	<String ID="FormerTokenExchangePoint" CONTENT="The main points of the token exchange system before the change"/>
	<String ID="TokenExchangePointAdded" CONTENT="Automatic token exchange system point-to-point number"/>
	<String ID="TokenExchangePoint" CONTENT="Auto Token Exchange Point"/>
	<String ID="LT_TOKEN_EXCHANGE_USE_TOKEN_POINT" CONTENT="Use a pointer to get an automatic token exchange system target item"/>
	<String ID="UsedTokenExchangePoint" CONTENT="Used Token ExchangePoint"/>
	<String ID="RestTokenExchangePoint" CONTENT="Remaining Token Points"/>
	<String ID="NAVMENU_TokenExchange_Short" CONTENT="Token Exchange"/>
	<String ID="NAVMENU_TokenExchange_Full" CONTENT="Token Exchange Information"/>
	<String ID="LT_TOKEN_EXCHANGE_TOKEN_ITEM_ACQUIRE" CONTENT="Number of tokens earned"/>
	<String ID="TokenExchangeId_short" CONTENT="TokenId"/>
	<String ID="TokenExchangeName_short" CONTENT="Relic Name"/>
	<String ID="TokenExchangeObtained_short" CONTENT="Obtain Device"/>
	<String ID="TokenExchangePoint_short" CONTENT="Token Point"/>
	<String ID="TokenExchangePointModify" CONTENT="Do you want to change token points?"/>
	<String ID="Obtained" CONTENT="Obtained"/>
	<String ID="NotObtained" CONTENT="Not Obtained"/>
	<String ID="NAVMENU_NpcArena_Short" CONTENT="NPC Arena"/>
	<String ID="NAVMENU_NpcArena_Full" CONTENT="NPC Arena"/>
	<String ID="LT_NPC_ARENA_CREATE" CONTENT="Create Arena"/>
	<String ID="LT_NPC_ARENA_START" CONTENT="Start Arena"/>
	<String ID="LT_NPC_ARENA_FIGHTER_INFO" CONTENT="Arena Npc Information"/>
	<String ID="LT_NPC_ARENA_END" CONTENT="Arena End"/>
	<String ID="HuntingzoneEventTypeDropCount" CONTENT="Increased number of item drops"/>
	<String ID="LT_USER_BETTING" CONTENT="User meter reading"/>
	<String ID="BettingContentsType" CONTENT="Betting Type"/>
	<String ID="BettingTemplateId" CONTENT="Betting"/>
	<String ID="BettingSelectedTeamId" CONTENT="Selected ID"/>
	<String ID="BettingItemId" CONTENT="Betting Item ID"/>
	<String ID="BettingItemCount" CONTENT="Betting Item Count"/>
	<String ID="NpcArenaId" CONTENT="Npc ArenaId"/>
	<String ID="NpcArenaInstanceId" CONTENT="Npc Arena InstanceId"/>
	<String ID="AiId" CONTENT="AI ID"/>
	<String ID="NpcArenaTeamId" CONTENT="Npc Arena TeamId"/>
	<String ID="NpcArenaWinnerId" CONTENT="Npc Arena WinnerId"/>
	<String ID="NpcArenaEndReason" CONTENT="Npc Arena End Reason"/>
	<String ID="NpcArenaActivateState" CONTENT="NpcArenaActivateState"/>
	<String ID="NpcArenaActivate" CONTENT="Activate"/>
	<String ID="NpcArenaDeActivate" CONTENT="Disable"/>
	<String ID="NpcArenaActivateSubmit" CONTENT="NpcArenaActivateSubmit"/>
	<String ID="NpcArenaActivateResult" CONTENT="NpcArenaActivateResult"/>
	<String ID="NpcArenaActivateSetting" CONTENT="NpcArenaActivateSetting"/>
	<String ID="ServerType" CONTENT="Server Type"/>
	<String ID="NormalServer" CONTENT="Normal Server"/>
	<String ID="DungeonServer" CONTENT="Dungeon Server"/>
	<String ID="ChangeServerType" CONTENT="Change Server Type"/>
	<String ID="BattleFieldServer" CONTENT="BattleField Server"/>
	<String ID="BattleFieldControlServer" CONTENT="BattleField Control Server"/>
	<String ID="NAVMENU_BattleFieldEvent_Short" CONTENT="Battlefield Reward Incremental Event"/>
	<String ID="NAVMENU_BattleFieldEvent_Full" CONTENT="Battlefield Reward Incremental Event"/>
	<String ID="BattleFieldEvent" CONTENT="BattleFieldEvent"/>
	<String ID="BattleFieldEventSet" CONTENT="BattleFieldEventSet"/>
	<String ID="BattleFieldEventDelete" CONTENT="Delete Battlefield Event"/>
	<String ID="BattleFieldEventSetViewTitle" CONTENT="Apply Battlefield Event"/>
	<String ID="HET_BATTLE_FIELD_POINT" CONTENT="Increase the Multiplier of Battlefield Reputation Points"/>
	<String ID="LT_FATIGABILITY_RECOVERY" CONTENT="Productivity Recovery"/>
	<String ID="FatigabilityType" CONTENT="Fatigability Type"/>
	<String ID="FatigabilityPoint" CONTENT="Fatigability Point"/>
	<String ID="LT_FATIGABILITY_CONSUME" CONTENT="Productivity Consumption"/>
	<String ID="FatigabilityReason" CONTENT="Fatigability Reason"/>
	<String ID="LT_PROMOTION_UPDATE" CONTENT="Promotion Update"/>
	<String ID="LT_PROMOTION_RESET" CONTENT="Promotion Reset"/>
	<String ID="LT_PROMOTION_CONDITION_INFO" CONTENT="Promotion Status Information"/>
	<String ID="PromotionType" CONTENT="Promotion Type"/>
	<String ID="PromotionId" CONTENT="PromotionId"/>
	<String ID="PromotionGrade" CONTENT="Promotion Grade"/>
	<String ID="PromotionConditionType" CONTENT="Promotion Condition Type"/>
	<String ID="PromotionConditionId" CONTENT="Promotion ConditionId"/>
	<String ID="PromotionConditionPoint" CONTENT="Promotion Condition Point"/>
	<String ID="FBPoint" CONTENT="Productivity"/>
	<String ID="ChangeUserFatigabilityPoint" CONTENT="Change Character Productivity Point"/>
	<String ID="ConfirmChangeUserFatigabilityPoint" CONTENT="Please check character productivity"/>
	<String ID="ChangePromotionGrade" CONTENT="Change Promotion Grade"/>
	<String ID="ChangeUserPromotionGrade" CONTENT="Change User Promotion Grade"/>
	<String ID="ConfirmChangeUserPromotionGrade" CONTENT="Confirm Change User Promotion Grade"/>
	<String ID="ChangePromotionConditionPoint" CONTENT="Change Promotion Condition Point"/>
	<String ID="ChangeUserPromotionConditionPoint" CONTENT="Change User Promotion Condition Point"/>
	<String ID="ConfirmChangeUserPromotionConditionPoint" CONTENT="Confirm Change User Promotion Condition Point"/>
	<String ID="LogPreset" CONTENT="Log group setting function"/>
	<String ID="LogPresetName" CONTENT="Group Name"/>
	<String ID="LogPresetAdd" CONTENT="Add Group"/>
	<String ID="LogPresetEdit" CONTENT="Edit group"/>
	<String ID="LogPresetDelete" CONTENT="Delete Group"/>
	<String ID="LogPresetList" CONTENT="Generated log group list"/>
	<String ID="CheckBox" CONTENT="CheckBox"/>
	<String ID="DungeonOnOff" CONTENT="Dungeon OnOff"/>
	<String ID="NAVMENU_DungeonOnOff_Short" CONTENT="Dungeon On/Off"/>
	<String ID="NAVMENU_DungeonOnOff_Full" CONTENT="Dungeon On/Off"/>
	<String ID="DungeonStatus" CONTENT="Dungeon Status"/>
	<String ID="DungeonControl" CONTENT="Dungeon Control"/>
	<String ID="DungeonOn" CONTENT="DungeonON"/>
	<String ID="DungeonOff" CONTENT="DungeonOFF"/>
	<String ID="Disconnected" CONTENT="Disconnected"/>
	<String ID="RestrictBulkOp" CONTENT="Restrict BulkOp"/>
	<String ID="AddNewFile" CONTENT="Add New File"/>
	<String ID="RestrictType" CONTENT="Restriction Type"/>
	<String ID="NAVMENU_RestrictBulkOp_Short" CONTENT="Bulk Restrict"/>
	<String ID="NAVMENU_RestrictBulkOp_Full" CONTENT="Bulk Restrict"/>
	<String ID="BulkOperationType" CONTENT="BulkOperationType"/>
	<String ID="OperatorName" CONTENT="OperatorName"/>
	<String ID="BulkFileName" CONTENT="BulkFileName"/>
	<String ID="RequestedTime" CONTENT="RequestedTime"/>
	<String ID="RestrictBulkDetail" CONTENT="RestrictBulkDetail"/>
	<String ID="BulkInserted" CONTENT="BulkInserted"/>
	<String ID="BulkRequested" CONTENT="BulkRequested"/>
	<String ID="BulkRequestSuccess" CONTENT="Success"/>
	<String ID="BulkRequestFailed" CONTENT="Failed"/>
	<String ID="BulkRevertRequested" CONTENT="Return request"/>
	<String ID="BulkRevertSuccess" CONTENT="Revert success"/>
	<String ID="BulkRevertFailed" CONTENT="Revert failed"/>
	<String ID="RestrictStartTime" CONTENT="Start"/>
	<String ID="RestrictEndTime" CONTENT="End"/>
	<String ID="RequestCount" CONTENT="Number of requests"/>
	<String ID="RestrictBulkFile" CONTENT="Request Information"/>
	<String ID="EventMatchingControl" CONTENT="EventMatchingControl"/>
	<String ID="NAVMENU_EventMatchingControl_Short" CONTENT="Event Matching Control"/>
	<String ID="NAVMENU_EventMatchingControl_Full" CONTENT="Event Matching Control"/>
	<String ID="BattleField" CONTENT="BattleField"/>
	<String ID="EventName" CONTENT="Event Name"/>
	<String ID="EventControl" CONTENT="Event Control"/>
	<String ID="AE_NONE" CONTENT="Done"/>
	<String ID="AE_ERROR" CONTENT="Server Error"/>
	<String ID="AE_CHAR_NOT_FOUND" CONTENT="Target user not found"/>
	<String ID="AE_INVALID_PARAM" CONTENT="Invalid parameter"/>
	<String ID="AE_ADD_ITEM_ERROR" CONTENT="Item addition failed"/>
	<String ID="AE_DEL_ITEM_ERROR" CONTENT="Item deletion failed"/>
	<String ID="AE_DEL_ITEM_ERROR_LOGIN" CONTENT="Login user cannot delete installation items"/>
	<String ID="AE_CHANGE_MONEY_ERROR" CONTENT="Failed to exchange money"/>
	<String ID="AE_DELETE_QUEST_ERROR" CONTENT="Task deletion failed"/>
	<String ID="AE_ADD_QUEST_ERROR" CONTENT="Quasar append failed"/>
	<String ID="AE_ADD_QUEST_ERROR_EXIST" CONTENT="Cannot add existing quasar"/>
	<String ID="AE_EDIT_QUEST_ERROR" CONTENT="quest deletion failed"/>
	<String ID="AE_ADD_SKILL_ERROR" CONTENT="Skill addition failed"/>
	<String ID="AE_DEL_SKILL_ERROR" CONTENT="Skill deletion failed"/>
	<String ID="AE_CHANGE_PROF_ERROR" CONTENT="Unable to replace character proficiency"/>
	<String ID="AE_CHANGE_CREST_ERROR" CONTENT="Cannot replace role statement"/>
	<String ID="AE_CHANGE_ACHIEVEMENT_ERROR" CONTENT="Cannot replace character achievement"/>
	<String ID="AE_ARTISAN_ERROR" CONTENT="Failed to add, delete and modify character creation skills"/>
	<String ID="AE_FRIEND_ERROR" CONTENT="Modify add role friend delete failed"/>
	<String ID="AE_GUILD_ERROR" CONTENT="Add character guild delete modification failed"/>
	<String ID="AE_ADD_RESTRICTION_ERROR" CONTENT="Add role sanctions failed"/>
	<String ID="AE_DEL_RESTRICTION_ERROR" CONTENT="Failed to delete role sanctions"/>
	<String ID="AE_UNDELETE_USER_ERROR" CONTENT="Failed to restore cleared role"/>
	<String ID="AE_RENAME_USER_ERROR" CONTENT="Rename role failed"/>
	<String ID="AE_DEL_TRADE_BROKER_ITEM_ERROR" CONTENT="Failed to delete trade broker"/>
	<String ID="AE_CHANGE_SERVER_SETTING_ERROR" CONTENT="Cannot replace previous setting of role server"/>
	<String ID="AE_CHANGE_USER_LEVEL_ERROR" CONTENT="Failed to replace role level"/>
	<String ID="AE_CHANGE_USER_EXP_ERROR" CONTENT="Cannot replace character experience"/>
	<String ID="AE_STOP_VENDING_MACHINE_ERROR" CONTENT="Vending machine failed to stop"/>
	<String ID="AE_NOT_FOUND_PET_ERROR" CONTENT="No gangway pet"/>
	<String ID="AE_CHANGE_CREST_POINT_ERROR" CONTENT="Cannot replace character statement point"/>
	<String ID="AE_SEIZURE_ITEM_ERROR" CONTENT="Failed to seize item"/>
	<String ID="AE_RETURN_SEIZURE_ITEM_ERROR" CONTENT="Unable to return your seized item to the package"/>
	<String ID="AE_SEIZURE_MONEY_ERROR" CONTENT="Failed to deduct money"/>
	<String ID="AE_RETURN_SEIZURE_MONEY_ERROR" CONTENT="Unable to release deposit money"/>
	<String ID="AE_SEIZURE_PARCEL_ERROR" CONTENT="Parcel seizure failed"/>
	<String ID="AE_DELETE_SEIZURE_ITEM_ERROR" CONTENT="Failed to delete seizure item"/>
	<String ID="AE_DELETE_SEIZURE_MONEY_ERROR" CONTENT="Failed to delete deposit money item"/>
	<String ID="AE_EXECUTE_ADMIN_REWARD_ERROR" CONTENT="Operator bonus payment failed"/>
	<String ID="AE_CHANGE_USER_PKPOINT_ERROR" CONTENT="Failed to modify character's PK point"/>
	<String ID="AE_CHANGE_REPUTATION_EXP_ERROR" CONTENT="Failed to modify character's reputation experience"/>
	<String ID="AE_CHANGE_REPUTATION_POINT_ERROR" CONTENT="Failed to modify character's reputation point"/>
	<String ID="AE_ADD_NEW_NPCGUILD_ERROR" CONTENT="Cannot add new reputation to character"/>
	<String ID="AE_NO_EXIST_GUILD_WAR" CONTENT="Before Guild Exist"/>
	<String ID="AE_ADD_WORLD_FESTIVAL_SCHEDULE_ERROR" CONTENT="Event scheduling failed (probably timed repetition)"/>
	<String ID="AE_CANCEL_WORLD_FESTIVAL_SCHEDULE_ERROR" CONTENT="Event scheduling failed (probably timed repetition)"/>
	<String ID="AE_CHANGE_BATTLE_FIELD_INFO_ERROR" CONTENT="Battlefield win, no, lose fix failed"/>
	<String ID="AE_CHANGE_BATTLE_FIELD_DETAILS_ERROR" CONTENT="Battlefield kill, death, fix failed"/>
	<String ID="AE_CHANGE_BATTLE_FIELD_GRADESCORE_ERROR" CONTENT="Battlefield rating correction failed"/>
	<String ID="AE_CHANGE_BATTLE_FIELD_COUNT_STRONGHOLD_ERROR" CONTENT="Battlefield occupation correction failed"/>
	<String ID="AE_INVALID_UNION_ID" CONTENT="Irregular Union ID"/>
	<String ID="AE_CANNT_FIND_GUILD" CONTENT="Cannot find guild"/>
	<String ID="AE_CANNT_FIND_GUILD_MASTER" CONTENT="Cannot find Gilt Master"/>
	<String ID="AE_EXIST_CLASS" CONTENT="Existing Alliance Class"/>
	<String ID="AE_INVALID_CLASS" CONTENT="Wrong class of Alliance"/>
	<String ID="AE_CANNT_APPOINT_COMMANDER_ANY_MORE" CONTENT="Can no longer be designated as Alliance Commander"/>
	<String ID="AE_CANNT_FIND_UNION_MEMBER" CONTENT="Union member not found"/>
	<String ID="AE_CANNT_FIND_UNION_GUILD" CONTENT="Could not find Union Guild"/>
	<String ID="AE_CHANGE_UNION_MEMBER_POLICY_POINT_ERROR" CONTENT="Union Member Policy Point Correction Failed"/>
	<String ID="AE_CHANGE_UNION_MEMBER_SEASON_POINT_ERROR" CONTENT="Failed to correct the league member's season points"/>
	<String ID="AE_CHANGE_UNION_MEMBER_TOTAL_SEASON_POINT_ERROR" CONTENT="Failed to correct total league season points"/>
	<String ID="AE_CHANGE_UNION_GUILD_SEASON_POINT_ERROR" CONTENT="Union Guild Season Point Correction Failed"/>
	<String ID="AE_CHANGE_UNION_GUILD_TOTAL_SEASON_POINT_ERROR" CONTENT="Union Guild Total Season Point Correction Failed"/>
	<String ID="AE_CANNT_BAN_CONSUL_GUILD" CONTENT="Archon Guid's exile failed"/>
	<String ID="AE_CHANGE_BATTLE_FIELD_RECORD_ERROR" CONTENT="Failed to modify battlefield record information"/>
	<String ID="AE_CHANGE_BATTLE_FIELD_BATTLEINFO_ERROR" CONTENT="Battlefield battle information correction failed"/>
	<String ID="AE_CHANGE_BATTLE_FIELD_EXTRA_INFO_ERROR" CONTENT="Failed to modify battlefield supplementary information"/>
	<String ID="AE_CHANGE_BATTLE_FIELD_GRADE_SCORE_ERROR" CONTENT="Battlefield rank score correction failed"/>
	<String ID="AE_CANNT_CHANGE_TAX_INVALID_TAX_RATE" CONTENT="Could not change due to incorrect tax percentage setting"/>
	<String ID="AE_CANNT_CHANGE_TAX_ERROR_DB" CONTENT="Tax rate modification failed due to database error"/>
	<String ID="AE_INVALID_ELITE" CONTENT="Wrong Elite Alliance Member"/>
	<String ID="AE_SAME_ELITE" CONTENT="Existing Elite Alliance Members"/>
	<String ID="AE_CANT_FIND_CONSUL" CONTENT="Cannot find transistor collector"/>
	<String ID="AE_INVALID_CONSUL_GUILD" CONTENT="Incorrect Consul Guild"/>
	<String ID="AE_MOVE_DISMISSED_GUILD_ITEM_ERROR" CONTENT="Transfer of disbanded Guild Warehouse item failed"/>
	<String ID="AE_CLEAR_DISMISSED_GUILD_WAREHOUSE_ERROR" CONTENT="Failed to clear disbanded stack"/>
	<String ID="AE_NOT_ENOUGH_INVENTORY" CONTENT="Not Enough Inventory"/>
	<String ID="AE_SEIZURE_BOUND_MONEY_ERROR" CONTENT="Failed to seize the gold under centralized control"/>
	<String ID="AE_RETURN_SEIZURE_BOUND_MONEY_ERROR" CONTENT="Failed to release vesting money withholding"/>
	<String ID="AE_DELETE_SEIZURE_BOUND_MONEY_ERROR" CONTENT="Failed to delete seized gold item"/>
	<String ID="AE_CHANGE_BOUND_MONEY_ERROR" CONTENT="Change attribution gold failed"/>
	<String ID="AE_STRING_LENGTH_ERROR" CONTENT="String Error"/>
	<String ID="RequestBulkOperationFile" CONTENT="Request Bulk Operation File"/>
	<String ID="BulkRequestType" CONTENT="Bulk Request Type"/>
	<String ID="Days" CONTENT="Days"/>
	<String ID="BulkProcessState" CONTENT="ProcessState"/>
	<String ID="RequestBulkOperationItem" CONTENT="Bulk work item request"/>
	<String ID="IdentityNo" CONTENT="Event Number"/>
	<String ID="BulkProcessResult" CONTENT="ProcessResult"/>
	<String ID="BulkOperation" CONTENT="Action"/>
	<String ID="CsvDownload" CONTENT="CSV Download"/>
	<String ID="HET_ENCHANT_RATE" CONTENT="Increase the Chance of Enhancement Success"/>
	<String ID="AddScheduledWorldFestival" CONTENT="Add Scheduled Event"/>
	<String ID="DeleteScheduledWorldFestival" CONTENT="Delete Scheduled Event"/>
	<String ID="AddWorldFestival" CONTENT="Add Event"/>
	<String ID="CompensationId" CONTENT="Compensation ID"/>
	<String ID="CompensationType" CONTENT="Type"/>
	<String ID="CompensationGold" CONTENT="Gold Coins"/>
	<String ID="BoundMoney" CONTENT="Bound Money"/>
	<String ID="ExtendInven" CONTENT="Extend Infrastructure"/>
	<String ID="Reputation" CONTENT="Reputation"/>
	<String ID="CompensationItemList" CONTENT="Item"/>
	<String ID="CompensationTypeBasic" CONTENT="General"/>
	<String ID="CompensationTypeAddition" CONTENT="Extra Random"/>
	<String ID="CompensationTypeCompletion" CONTENT="Repeat Completion"/>
	<String ID="CompensationTypeCare" CONTENT="Compensation Low Hook"/>
	<String ID="CompensationTypeSupport" CONTENT="Compensation High Hook"/>
	<String ID="CompensationBagTypeGiveAll" CONTENT="Pay All"/>
	<String ID="CompensationBagTypeSelect" CONTENT="Select Payment"/>
	<String ID="CompensationBagTypeRandom" CONTENT="Random selection"/>
	<String ID="CompensationBagTypeClass" CONTENT="By occupation"/>
	<String ID="CompensationBagTypeRace" CONTENT="By Race"/>
	<String ID="Quest" CONTENT="Quest"/>
	<String ID="DeleteCompletedQuest" CONTENT="Delete CompletedQuest"/>
	<String ID="GiveQuestCompensation" CONTENT="Compensation Period"/>
	<String ID="AE_CHANGE_AP_ERROR" CONTENT="A role AP replacement failed"/>
	<String ID="AE_EXPAND_INVEN_COUNT_ERROR" CONTENT="Character expansion failed"/>
	<String ID="AE_INCREASE_REPUTATION_EXP_AND_POINT_ERROR" CONTENT="Character evaluation experience value/point increase failed"/>
	<String ID="ResetInput" CONTENT="Re-input"/>
	<String ID="LT_OPERATOR_CHANGE_CHAR_SOCKET_COUNT" CONTENT="Change Operation Tool Role Slot"/>
	<String ID="BeforeCharSocketCount" CONTENT="Number of sockets before change"/>
	<String ID="ChangedCharSocketCount" CONTENT="Changed number of sockets"/>
	<String ID="LT_CHANGE_OWNER_ITEM_ENCHANT_SCROLL_INFO" CONTENT="EnchantScroll"/>
	<String ID="LT_INCREASE_SHARED_CHAR_SOCKET" CONTENT="[Account] Character Slot Extension"/>
	<String ID="Voice" CONTENT="Voice"/>
	<String ID="ChangeUserVoice" CONTENT="ChangeUserVoice"/>
	<String ID="ConfirmChangeUserVoice" CONTENT="ConfirmChangeUserVoice"/>
	<String ID="AE_ITEM_NOT_FOUND_IN_USER_INVEN" CONTENT="No items to find on a user basis"/>
	<String ID="AE_EXTENDED_SERVANT_INVALID" CONTENT="The battle carpet information is abnormal and the mission cannot be executed"/>
	<String ID="CannotAddEventBecauseDungeonTypeIsMismatched" CONTENT="The previous properties are inconsistent and the event cannot be added."/>
	<String ID="EventList" CONTENT="EventList"/>
	<String ID="NormalDungeon" CONTENT="Normal Dungeon"/>
	<String ID="NormalBattleField" CONTENT="Normal BattleField"/>
	<String ID="MissionQuest" CONTENT="MissionQuest"/>
	<String ID="DailyQuest" CONTENT="Daily Quest"/>
	<String ID="Guide" CONTENT="Guide"/>
	<String ID="Field" CONTENT="Field"/>
	<String ID="EventMatchingControlType" CONTENT="EventMatchingControlType"/>
	<String ID="EventMatchingControlList" CONTENT="[EventMatchingControlList]"/>
	<String ID="PlayGuideEventList" CONTENT="[PlayGuideEventList]"/>
	<String ID="PlayGuideEventSet" CONTENT="PlayGuideEventSet"/>
	<String ID="PlayGuideEventDel" CONTENT="PlayGuideEventDel"/>
	<String ID="DisableEventMatchingEvent" CONTENT="Status"/>
	<String ID="DisableEventMatchingCompensation" CONTENT="Accrued Compensation"/>
	<String ID="EventCompensationState" CONTENT="Reward Status"/>
	<String ID="EventDisabled" CONTENT="Event Disabled"/>
	<String ID="EventCompensationDisabled" CONTENT="Accruals"/>
	<String ID="AddControl" CONTENT="AddControl"/>
	<String ID="EnableEvent" CONTENT="Enable Event"/>
	<String ID="LT_USER_DAILY_PLAY_GUIDE_COMPENSATION_RECEIVED" CONTENT="Daily Play Guide Reward Payment"/>
	<String ID="LT_USER_DAILY_PLAY_GUIDE_PROGRESS_EXTRA_REWARD" CONTENT="Complete Adventurer's Extra Rewards Task"/>
	<String ID="LT_USER_DAILY_PLAY_GUIDE_ATTENDANCE_CHECK" CONTENT="Attendance Check"/>
	<String ID="LT_USER_DAILY_PLAY_GUIDE_MISSION_COMPLETE" CONTENT="Daily Play Guide Mission Complete"/>
	<String ID="DailyPlayGuideCompensationReceived" CONTENT="Daily PlayGuide Compensation Received"/>
	<String ID="DailyPlayGuideAttendanceCount" CONTENT="Daily Play Guide Attendance Count"/>
	<String ID="CompensationItem" CONTENT="Compensation Item"/>
	<String ID="ReceivedCount" CONTENT="Received Count"/>
	<String ID="CompleteMissionName" CONTENT="Complete Mission Name"/>
	<String ID="CompleteMissionId" CONTENT="CompleteMissionId"/>
	<String ID="ProgressInfo" CONTENT="Progress Info"/>
	<String ID="LT_MIX_ITEM_SUCCESS" CONTENT="Item Composition"/>
	<String ID="MixMaterialName" CONTENT="Material Sheet"/>
	<String ID="MixMaterialAmount" CONTENT="Material Amount"/>
	<String ID="ResultAmount" CONTENT="Result Amount"/>
	<String ID="LT_ITEM_ENCHANT_DECOMPOSE" CONTENT="Decompose into Object Enhancement Stone"/>
	<String ID="CumulatedEnchant" CONTENT="Cumulated Enchant Stone"/>
	<String ID="ChangeCumulatedEnchant" CONTENT="Change Cumulated Enchant"/>
	<String ID="NewCEnchant" CONTENT="NewCEnchant"/>
	<String ID="DecomposedItem" CONTENT="Decomposed Item"/>
	<String ID="LT_AWAKEN_TARGET_ITEM" CONTENT="Awaken Target Item"/>
	<String ID="LT_AWAKEN_DELETED_ITEMS" CONTENT="Awaken Deleted Items"/>
	<String ID="Awakened" CONTENT="Awakened"/>
	<String ID="LT_UNBINDER_USE" CONTENT="Unbind"/>
	<String ID="ChangeUnbindCount" CONTENT="Change Unbind Count"/>
	<String ID="UnbinderItem" CONTENT="UnbinderItem"/>
	<String ID="UnbindCount" CONTENT="UnbindCount"/>
	<String ID="ConfirmUnbind" CONTENT="Really unbind?"/>
	<String ID="IncreaseUnbindCount" CONTENT="Increase Unbind Count"/>
	<String ID="ConfirmChangeUnbind" CONTENT="Do you really want to change the number of cancellations?"/>
	<String ID="ConfirmChangeEnchant" CONTENT="Do you really want to change?"/>
	<String ID="OldUnbind" CONTENT="Old Unbinds"/>
	<String ID="NewUnbind" CONTENT="New Unbind"/>
	<String ID="BattleFieldResult" CONTENT="BattleFieldResult"/>
	<String ID="LT_CRISTAL_COMBINE_RESULT" CONTENT="Crystal Synthesis"/>
	<String ID="CrystalSucceedResult" CONTENT="Crystal Succeed Result"/>
	<String ID="CrystalTierUpResult" CONTENT="Crystal Tier Up Result"/>
	<String ID="CrystalUpgradeResult" CONTENT="Crystal Upgrade Result"/>
	<String ID="KickUserByAdmin" CONTENT="KickUserByAdmin"/>
	<String ID="MissionTaskId" CONTENT="MissionTaskId"/>
	<String ID="DailyAttendance" CONTENT="Daily Attendance"/>
	<String ID="DailyAttendanceInfo" CONTENT="Daily Attendance Info"/>
	<String ID="SetDailyAttendance" CONTENT="Set Daily Attendance"/>
	<String ID="EventMatchingList" CONTENT="Event Matching List"/>
	<String ID="EventMatchingTaskName" CONTENT="Daily Match Task Name"/>
	<String ID="ProgressMissionList" CONTENT="Progress Mission List"/>
	<String ID="CompleteMissionList" CONTENT="Completed Mission List"/>
	<String ID="MissionTaskName" CONTENT="Mission Task Name"/>
	<String ID="MissionName" CONTENT="Mission Name"/>
	<String ID="MissionId" CONTENT="MissionId"/>
	<String ID="MissionCompleteTime" CONTENT="Mission Completion Date"/>
	<String ID="Magnification" CONTENT="Magnification"/>
	<String ID="PlayGuideEventReward" CONTENT="Total Reward Increase"/>
	<String ID="PlayGuideEventDungeonCount" CONTENT="Increase the number of bet completions"/>
	<String ID="PlayGuideBattleFieldCount" CONTENT="Increase the number of battlefield completions"/>
	<String ID="PlayGuideCommonCount" CONTENT="Increase the total number of completions"/>
	<String ID="GuildWarStart" CONTENT="GuildWarStart"/>
	<String ID="GuildWarWin" CONTENT="GuildWarWin"/>
	<String ID="GuildWarLose" CONTENT="Guild War Lost"/>
	<String ID="GuildWarDraw" CONTENT="Guild War Draw"/>
	<String ID="GuildWarSurrender" CONTENT="GuildWarSurrender"/>
	<String ID="GuildWarGiveUp" CONTENT="GuildWarGiveUp"/>
	<String ID="GuildWarCancel" CONTENT="GuildWarCancel"/>
	<String ID="GuildWarCancelBySystem" CONTENT="Cancel"/>
	<String ID="UpdateGuildGeneralCoinBySystem" CONTENT="System Tactical Chip Payment"/>
	<String ID="ClearGuildGeneralCoinBySystem" CONTENT="System Tactical Chip Recovery"/>
	<String ID="UpdateGuildGeneralCoinByAdminTool" CONTENT="Change Tactical Chip with Operation Tool"/>
	<String ID="UpdateFloatingCastleCoinByGCReward" CONTENT="Gilter Competitive Actuarial Compensation"/>
	<String ID="UpdateFloatingCastleCoinByBuyParts" CONTENT="부유성 파츠 구입"/>
	<String ID="UpdateFloatingCastleCoinByAdmin" CONTENT="System Payment"/>
	<String ID="ContinentId" CONTENT="Continent ID"/>
	<String ID="FloatingCasteId" CONTENT="Plankton ID"/>
	<String ID="FloatingCastePos" CONTENT="Plankton coordinates"/>
	<String ID="FloatingCasteOwnerGuild" CONTENT="The Rich Guild"/>
	<String ID="FloatingCasteOwnerGuildDbId" CONTENT="GildDBID"/>
	<String ID="ChiefName" CONTENT="Guild Hall Name"/>
	<String ID="ChiefDbId" CONTENT="Guild Hall DBID"/>
	<String ID="CHANGE_FLOATING_CASTLE_OWNER" CONTENT="Change rich owner"/>
	<String ID="FloatingCastleId" CONTENT="floatingCastleId"/>
	<String ID="FloatingCastleOwnerGuildDbId" CONTENT="guildDbId"/>
	<String ID="DEL_GUILD_FLAG" CONTENT="Delete Guild Flag"/>
	<String ID="GuildDbId" CONTENT="guildDbId"/>
	<String ID="FloatingCastlePartsInven" CONTENT="Floating Castle Parts"/>
	<String ID="FloatingCastlePartsTemplateId" CONTENT="FloatingCastlePartsTemplateId"/>
	<String ID="FloatingCastlePartsAmount" CONTENT="FloatingCastlePartsAmount"/>
	<String ID="FloatingCastleParts" CONTENT="FloatingCastleParts"/>
	<String ID="FloatingCastlePartsSlot" CONTENT="FloatingCastlePartsSlot"/>
	<String ID="FloatingCastlePartsLocation" CONTENT="PartsLocation"/>
	<String ID="FloatingCastlePartsPosition" CONTENT="PastingCastlePartsPosition"/>
	<String ID="FloatingCastlePartsName" CONTENT="PastlePartsName"/>
	<String ID="GuildCompetitionSkill" CONTENT="Guild Skills"/>
	<String ID="GuildCompetitionSkillChief" CONTENT="Permission"/>
	<String ID="GuildCompetitionSkillPerm" CONTENT="Use period"/>
	<String ID="GuildCompetition" CONTENT="Guild Competition"/>
	<String ID="GCParticipation" CONTENT="Participation in guild competitions"/>
	<String ID="GCSeasonStart" CONTENT="Start of the guild competition season"/>
	<String ID="GCResult" CONTENT="Guild competition results"/>
	<String ID="GCPersonalScore" CONTENT="Individual result in the guild competition"/>
	<String ID="GCGuildScore" CONTENT="Guild Scoring"/>
	<String ID="GCContentsRanking" CONTENT="Guild Competition Ranking"/>
	<String ID="GCRewards" CONTENT="Rewards for guild competitions"/>
	<String ID="GCSeasonNum" CONTENT="Season"/>
	<String ID="GCLeague" CONTENT="League"/>
	<String ID="GCOldSeason" CONTENT="Previous Season"/>
	<String ID="GCNewSeason" CONTENT="New Season"/>
	<String ID="GCContents" CONTENT="Content"/>
	<String ID="GCStatus" CONTENT="Request guild competition status"/>
	<String ID="GCRanking" CONTENT="Competition Rating"/>
	<String ID="GCScore" CONTENT="Guild Competition Points"/>
	<String ID="DISABLE_GUILD_COMPETITION" CONTENT="Control of participation in guild competitions"/>
	<String ID="ENABLE_GUILD_COMPETITION" CONTENT="The ban on guild competitions has been lifted"/>
	<String ID="ADD_GUILD_COMPETITION_SKILL" CONTENT="Add guild skill"/>
	<String ID="DISABLE_GUILD_COMPETITION_CONTENTS" CONTENT="Control of guild competitions"/>
	<String ID="DELETE_GUILD_COMPETITION_SKILL" CONTENT="Delete guild skill"/>
	<String ID="CHANGE_GUILD_COMPETITION_SCORE" CONTENT="Adjustment of points in guild competitions"/>
	<String ID="ContentId" CONTENT="Content ID"/>
	<String ID="IsPerm" CONTENT="Period of use"/>
	<String ID="IsChief" CONTENT="Resolution"/>
	<String ID="UserDbId" CONTENT="User DBID"/>
	<String ID="HIDE_GUILD_COMPETITION_CONTENT" CONTENT="Stop guild competition content"/>
	<String ID="SkillId" CONTENT="skillId"/>
	<String ID="IsChief_true" CONTENT="Guild Head"/>
	<String ID="IsChief_false" CONTENT="Guild Member"/>
	<String ID="IsPerm_true" CONTENT="Permanent"/>
	<String ID="IsPerm_false" CONTENT="Seasonal"/>
	<String ID="NoPoint" CONTENT="Failed to get points (can continue)"/>
	<String ID="GCContStatus" CONTENT="Guild Competition Status"/>
	<String ID="DisableType" CONTENT="Disable Type"/>
	<String ID="GCManStatus" CONTENT="Progress"/>
	<String ID="GCMan" CONTENT="Control of guild competitions"/>
	<String ID="CanGetPoint" CONTENT="Whether to get points"/>
	<String ID="Parti_true" CONTENT="Can participate"/>
	<String ID="Parti_false" CONTENT="Cannot participate"/>
	<String ID="RewardType" CONTENT="Reward Type"/>
	<String ID="RT_FloatingCastle" CONTENT="FloatingCastle"/>
	<String ID="RT_Skill" CONTENT="Skill"/>
	<String ID="RT_Emblem" CONTENT="Emblem"/>
	<String ID="RT_Flag" CONTENT="Flag"/>
	<String ID="RT_CastleCoin" CONTENT="Castle Coin"/>
	<String ID="BeforeScore" CONTENT="Before Change"/>
	<String ID="AfterScore" CONTENT="After Score"/>
	<String ID="ADD_FLOATING_CASTLE_PARTS_INVEN" CONTENT="Add suspended particles"/>
	<String ID="PartsAmount" CONTENT="PartsAmount"/>
	<String ID="PartsId" CONTENT="PartsId"/>
	<String ID="DEL_FLOATING_CASTLE_PARTS_INVEN" CONTENT="Delete Floating Potts"/>
	<String ID="NotInGuild" CONTENT="Cannot change the score of each guild because no guild belongs to."/>
	<String ID="GotoBattlefield" CONTENT="Depends on Battlefield Rank"/>
	<String ID="GotoGuildWar" CONTENT="Depends on tactical chip holdings"/>
	<String ID="GotoAchievement" CONTENT="Depends on total performance score"/>
	<String ID="DEL_FLOATING_CASTLE_PARTS" CONTENT="Delete Floating Potts"/>
	<String ID="NoSuchQuest" CONTENT="Quest not found"/>
	<String ID="AssignFloatingCastle" CONTENT="Assign FloatingCastle"/>
	<String ID="CollectFloatingCastle" CONTENT="Floating Recycling"/>
	<String ID="RegisterFloatingCastleParts" CONTENT="Installing FloatingCastleParts"/>
	<String ID="UnRegisterFloatingCastleParts" CONTENT="Uninstall suspended particles"/>
	<String ID="FloatingCastleCostState" CONTENT="Floating Maintenance Cost"/>
	<String ID="FloatingCastleCost" CONTENT="Floating Maintenance Cost"/>
	<String ID="LT_USER_FLOATING_CASLTE_PARTS_BUY" CONTENT="Buy Float"/>
	<String ID="LT_INSERT_FLOATING_CASTLE_PARTS_INVEN" CONTENT="Archive parts to warehouse"/>
	<String ID="InsertFloatingCastlePartInvenType" CONTENT="How to deposit parts into warehouse"/>
	<String ID="EmblemId" CONTENT="EmblemId"/>
	<String ID="IsForeverEmblem_false" CONTENT="false"/>
	<String ID="IsForeverEmblem_true" CONTENT="true"/>
	<String ID="IsForeverEmblem" CONTENT="Is ForeverEmblem"/>
	<String ID="UPDATE_GUILD_EMBLEM" CONTENT="Change Guild Logo"/>
	<String ID="SeachFloatingCastle" CONTENT="Search floating guild"/>
	<String ID="PartsStoreOn" CONTENT="Store Status"/>
	<String ID="PartsStoreStateOn" CONTENT="Store On"/>
	<String ID="PartsStoreStateOff" CONTENT="Store Off"/>
	<String ID="UPDATE_FLOATING_CASTLE_STORE_STATE" CONTENT="Fatz Store Control"/>
	<String ID="FloatingCastlePartsCoin" CONTENT="Number of Parts Coins"/>
	<String ID="CHANGE_FLOATING_CASTLE_PARTS_COIN" CONTENT="Change Paz"/>
	<String ID="LT_GUILD_COMPETITION_RENEWAL_RESULT" CONTENT="Guild Competition Interim Settlement Results"/>
	<String ID="SeriouslyDelete" CONTENT="Are you sure you want to delete the role?"/>
	<String ID="DownloadAll" CONTENT="Bulk Download"/>
	<String ID="ReallyDownload" CONTENT="This operation may take a long time. Are you sure you want to run?"/>
	<String ID="WarningDbLoad" CONTENT="This operation can put a lot of load on the DB, so it should be run while the server is down."/>
	<String ID="NoSet" CONTENT="Not set"/>
	<String ID="GiveOnFirstLogin" CONTENT="Original Payment (1 minute)"/>
	<String ID="DailyReset" CONTENT="Daily Reset"/>
	<String ID="WrongInput" CONTENT="Invalid input. Please check item TID or quantity."/>
	<String ID="Index" CONTENT="Session"/>
	<String ID="ConfirmDeleteAllMailEvent" CONTENT="Delete all mail event event. This action is irreversible. Are you sure you want to do this?"/>
	<String ID="SelectServer" CONTENT="Select Server"/>
	<String ID="UserSocialList" CONTENT="User Social List"/>
	<String ID="DEL_USER_SOCIAL" CONTENT="Delete User Social"/>
	<String ID="NAVMENU_UserSocial_Short" CONTENT="User Social"/>
	<String ID="SocialId" CONTENT="SocialId"/>
	<String ID="SocialIdName" CONTENT="SocialIdName"/>
	<String ID="ADD_USER_SOCIAL" CONTENT="Add User Social"/>
	<String ID="INVALID_ITEM_TEMPLATE_ID" CONTENT="Item does not exist"/>
	<String ID="DOWNLOAD_STATISTICS" CONTENT="Download Server Statistics Excel"/>
	<String ID="QueryName" CONTENT="Statistics"/>
	<String ID="IncludeDeletedGuilds" CONTENT="Include Deleted Guilds"/>
	<String ID="PRODUCT_SALE_EVENT" CONTENT="Boboli Token Mall Discounted Product Settings"/>
	<String ID="CURRENT_PRODUCT_SALE_EVENT" CONTENT="Sales item set"/>
	<String ID="DefaultProductPrice" CONTENT="Sales Price"/>
	<String ID="DEL_PRODUCT_SALE_EVENT" CONTENT="Delete discounted item"/>
	<String ID="SalePrice" CONTENT="Sale Price"/>
	<String ID="ItemTemplateId" CONTENT="ItemTemplateId"/>
	<String ID="ProductSaleEventId" CONTENT="Product Sale EventId"/>
	<String ID="ADD_PRODUCT_SALE_EVENT" CONTENT="Add Sale Item"/>
	<String ID="UserDBId" CONTENT="User DBID"/>
	<String ID="NAVMENU_ProductSaleEvent_Short" CONTENT="Product Sale Event"/>
	<String ID="NAVMENU_ProductSaleEvent_Full" CONTENT="Product Sale Event"/>
	<String ID="WrongSendMinutes" CONTENT="Invalid value entered for play time. Please enter an integer between 1 and 1440."/>
	<String ID="WrongStartTime" CONTENT="Start time cannot be later than end time."/>
	<String ID="DELETE_USER" CONTENT="Delete User"/>
	<String ID="SkillLearn_Method" CONTENT="Learn Method"/>
	<String ID="SkillLearn_Npc" CONTENT="Combat Instructor"/>
	<String ID="SkillLearn_Book" CONTENT="Skill Tutorial"/>
	<String ID="SkillLearn_Lord" CONTENT="Lord Skill"/>
	<String ID="SkillLearn_Account" CONTENT="Internet Cafe Skills"/>
	<String ID="SkillLearn_ShortCut" CONTENT="Auto Learning"/>
	<String ID="SkillLearn_Ranker" CONTENT="Battlefield Ranking"/>
	<String ID="SkillLearn_Ui" CONTENT="Skill UI"/>
	<String ID="SkillLearn_Quest" CONTENT="Quest"/>
	<String ID="ExportExcelStartPage" CONTENT="Excel Export StartPage"/>
	<String ID="ExportExcelEndPage" CONTENT="Excel Export EndPage"/>
	<String ID="Current_Channel_State" CONTENT="Current Channel State"/>
	<String ID="ChannelingChannel" CONTENT="ChannelingChannel"/>
	<String ID="BattleFieldChannel" CONTENT="BattleFieldChannel"/>
	<String ID="DungeonChannel" CONTENT="Dungeon Channel"/>
	<String ID="DefaultChannelCount" CONTENT="Default Channel Count"/>
	<String ID="ChangeChannelCount" CONTENT="Change Channel Count"/>
	<String ID="AddChannelCount" CONTENT="Add Channel Count"/>
	<String ID="ADD_CONTINENT_CHANNEL_COUNT" CONTENT="Add Continent Channel Count"/>
	<String ID="AllHuntingZone" CONTENT="All Hunting Zones"/>
	<String ID="NAVMENU_ChannelMonitor_Short" CONTENT="Channel Monitor"/>
	<String ID="NAVMENU_ChannelMonitor_Full" CONTENT="Channel Monitor"/>
	<String ID="DoControl" CONTENT="Control"/>
	<String ID="ClassSetting" CONTENT="Application Class"/>
	<String ID="InvalidClassSetting" CONTENT=", the entire class target will proceed."/>
	<String ID="ServantList" CONTENT="Servant List"/>
	<String ID="ServantDefaultName" CONTENT="Servant Default Name"/>
	<String ID="NowServantName" CONTENT="Now Servant Name"/>
	<String ID="ServantEnergy" CONTENT="Servant Energy"/>
	<String ID="NAVMENU_Servant_Short" CONTENT="Servant"/>
	<String ID="NAVMENU_Servant_Full" CONTENT="Servant"/>
	<String ID="ChangeName" CONTENT="ChangeName"/>
	<String ID="BeforeName" CONTENT="BeforeName"/>
	<String ID="CHANGE_SERVANT_NAME" CONTENT="Rename Servant"/>
	<String ID="ConfirmChangeServantName" CONTENT="ConfirmChangeServantName"/>
	<String ID="ItemString" CONTENT="ItemString"/>
	<String ID="OwnerDbId" CONTENT="ownerDbId"/>
	<String ID="CHANGE_ITEM_STRING" CONTENT="Change Costume Shop Item Label"/>
	<String ID="ItemStringTooLong" CONTENT="Cannot exceed 8 English letters or 4 Korean letters."/>
	<String ID="ItemStringCannotContainSpaces" CONTENT="No spaces should be entered."/>
	<String ID="Off" CONTENT="Off"/>
	<String ID="On" CONTENT="On"/>
	<String ID="CHANGE_BATTLE_FIELD_ON_OFF" CONTENT="Battlefield ON/OFF"/>
	<String ID="ChangeState" CONTENT="Change State"/>
	<String ID="MoveDismissedGuildWarehouseItemsToNewGuild" CONTENT="Move Guild Warehouse"/>
	<String ID="DismissedGuildDbId" CONTENT="Dismissed Guild DBID"/>
	<String ID="NewGuildDbId" CONTENT="Target Guild DBID"/>
	<String ID="DismissedGuildName" CONTENT="Dismissed Guild Name"/>
	<String ID="NotDismissedGuild" CONTENT="Not Dismissed Guild"/>
</Strings>
