<QuestDialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="https://vezel.dev/novadrop/dc/QuestDialog QuestDialog.xsd" id="36" huntingZoneId="33" voiceTypeId="0" xmlns="https://vezel.dev/novadrop/dc/QuestDialog">
    <Text id="1" huntingZoneId="0" prevId="0" villagerId="0" />
    <Text id="100" huntingZoneId="233" prevId="0" villagerId="1008">
        <Page social="0" />
    </Text>
    <Text id="2" huntingZoneId="233" prevId="0" villagerId="1008">
        <Page social="4">Enough of this fighting hallway by hallway, stairway by stairway. Time to apply some Mysterium ingenuity to the problem.&lt;BR&gt;&lt;BR&gt;Take this scrambler. Install it each of the three ecesis devices...in the right order.&lt;BR&gt;&lt;BR&gt;That should starve his invisible ghosts of energy. And with the ghosts out of the picture, <PERSON><PERSON><PERSON> will lose control of the undead.</Page>
    </Text>
    <Text id="3" huntingZoneId="233" prevId="2" villagerId="1015">
        <Page social="0">(You install the scrambler. The ecesis device stops humming, groans as if alive, then goes silent. The atmosphere in this chamber seems less stifling.)</Page>
    </Text>
    <Text id="4" huntingZoneId="233" prevId="3" villagerId="1016">
        <Page social="0">(You install the scrambler. The ecesis device stops humming, groans as if alive, then goes silent. The atmosphere in this chamber seems less stifling.)</Page>
    </Text>
    <Text id="5" huntingZoneId="233" prevId="4" villagerId="1017">
        <Page social="0">(You install the scrambler. The ecesis device stops humming, groans as if alive, then goes silent. The atmosphere in this chamber seems less stifling.)</Page>
    </Text>
    <Text id="6" huntingZoneId="233" prevId="5" villagerId="1008">
        <Page social="4">Now Thulsa will have great difficulty giving the undead the necromantic power they need.&lt;BR&gt;&lt;BR&gt;A flawless victory, &lt;PCNAME&gt;.</Page>
    </Text>
</QuestDialog>
